package com.dcas.common.utils.params;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ResponseConverter {

    /**
     * 将HTTP接口响应内容和响应格式配置转换为IntegrationFormFieldVO列表
     *
     * <p>处理流程：</p>
     * <ol>
     *   <li>解析JSON响应数据</li>
     *   <li>应用筛选条件过滤数据（如果有）</li>
     *   <li>从筛选后的数据中提取需要保存的字段</li>
     *   <li>转换为IntegrationFormFieldVO格式供下一接口使用</li>
     * </ol>
     *
     * <p>使用示例：</p>
     * <pre>
     * // 响应数据
     * String response = "{\"data\":[{\"id\":1,\"name\":\"John\",\"age\":25},{\"id\":2,\"name\":\"Jane\",\"age\":30}]}";
     *
     * // 筛选条件：年龄大于等于25
     * IntegrationFormFieldVO filter = new IntegrationFormFieldVO();
     * filter.setName("age");
     * filter.setLocation("data");
     * filter.setValue("25");
     * filter.setType("gte");
     * filter.setParamType(2);
     * filter.setDataRequired(2);
     * filter.setColumnType("INTEGER");
     *
     * // 保存字段：姓名
     * InParam.ColumnParam saveColumn = new InParam.ColumnParam();
     * saveColumn.setColumnName("name");
     * saveColumn.setLocation("data");
     * saveColumn.setType(2);
     * saveColumn.setColumnType("STRING");
     *
     * List&lt;IntegrationFormFieldVO&gt; result = convertToFormFields(response, outParam,
     *     Arrays.asList(filter), Arrays.asList(saveColumn));
     * // 结果：只包含年龄>=25的用户姓名
     * </pre>
     *
     * @param response HTTP接口响应内容（JSON格式）
     * @param outParam 响应格式配置,记录了响应内容字段的字段类型以及层级关系等
     * @param filterParams 配置的筛选条件（paramType=2且dataRequired=2的条件会被应用）
     * @param saveColumns 配置的保存字段（type=2表示来自接口结果的字段）
     * @return IntegrationFormFieldVO列表，包含筛选和转换后的字段数据
     */
    public static List<IntegrationFormFieldVO> convertToFormFields(String response, OutParam outParam,
                                                                   List<IntegrationFormFieldVO> filterParams,
                                                                   List<InParam.ColumnParam> saveColumns) {
        if (StringUtils.isBlank(response) || outParam == null || CollUtil.isEmpty(saveColumns)) {
            log.warn("响应数据为空或配置参数缺失，返回空列表");
            return new ArrayList<>();
        }

        try {
            // 解析响应JSON
            Object responseData = JSONUtil.parse(response);

            // 应用筛选条件过滤响应数据
            Object filteredData = applyFilterConditions(responseData, filterParams, outParam);

            // 提取需要保存的字段数据
            List<IntegrationFormFieldVO> result = extractSaveFields(filteredData, saveColumns, outParam);

            log.info("成功转换响应数据，提取到 {} 个字段", result.size());
            return result;

        } catch (Exception e) {
            log.error("转换响应数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 应用筛选条件过滤响应数据
     *
     * <p>筛选逻辑说明：</p>
     * <ul>
     *   <li>只处理paramType=2（出参）且dataRequired=2（筛选条件）的过滤条件</li>
     *   <li>多个筛选条件使用AND逻辑，所有条件都必须满足</li>
     *   <li>支持的比较操作符：eq(等于), ne(不等于), gt(大于), lt(小于), gte(大于等于), lte(小于等于), contains(包含)</li>
     *   <li>操作符通过filter.type字段指定，默认为eq</li>
     *   <li>支持数值、字符串、布尔类型的比较</li>
     *   <li>对于数组数据，筛选出匹配的元素；对于对象数据，判断整个对象是否匹配</li>
     * </ul>
     *
     * <p>筛选条件配置示例：</p>
     * <pre>
     * IntegrationFormFieldVO filter = new IntegrationFormFieldVO();
     * filter.setName("age");           // 字段名
     * filter.setLocation("data/users"); // 字段位置路径
     * filter.setValue("25");           // 期望值
     * filter.setType("gte");           // 比较操作符：大于等于
     * filter.setParamType(2);          // 出参
     * filter.setDataRequired(2);       // 筛选条件
     * filter.setColumnType("INTEGER"); // 字段类型
     * </pre>
     *
     * @param responseData 原始响应数据
     * @param filterParams 筛选条件列表
     * @param outParam 响应格式配置（当前未使用，预留扩展）
     * @return 筛选后的数据，如果没有筛选条件或筛选失败则返回原始数据
     */
    private static Object applyFilterConditions(Object responseData, List<IntegrationFormFieldVO> filterParams, OutParam outParam) {
        if (responseData == null || filterParams == null || filterParams.isEmpty()) {
            return responseData;
        }

        // 筛选出参数类型为2（出参）且参数要求为2（筛选条件）的过滤条件
        List<IntegrationFormFieldVO> actualFilters = filterParams.stream()
                .filter(param -> param.getParamType() != null && param.getParamType() == 2
                        && param.getDataRequired() != null && param.getDataRequired() == 2
                        && StringUtils.isNotBlank(param.getName()) && StringUtils.isNotBlank(param.getValue()))
                .collect(Collectors.toList());

        if (actualFilters.isEmpty()) {
            log.debug("没有有效的筛选条件，返回原始数据");
            return responseData;
        }

        log.debug("开始应用 {} 个筛选条件进行数据过滤", actualFilters.size());

        try {
            // 应用筛选条件
            Object filteredData = applyFiltersToData(responseData, actualFilters);

            log.debug("筛选完成，筛选条件数量: {}", actualFilters.size());
            return filteredData;

        } catch (Exception e) {
            log.warn("应用筛选条件时发生异常: {}, 返回原始数据", e.getMessage());
            return responseData;
        }
    }

    /**
     * 对数据应用筛选条件
     */
    private static Object applyFiltersToData(Object data, List<IntegrationFormFieldVO> filters) {
        if (data == null || filters.isEmpty()) {
            return data;
        }

        // 根据数据类型应用不同的筛选策略
        if (data instanceof JSONArray) {
            return filterArrayData((JSONArray) data, filters);
        } else if (data instanceof List) {
            return filterListData((List<?>) data, filters);
        } else if (data instanceof JSONObject || data instanceof Map) {
            return filterObjectData(data, filters);
        } else {
            // 对于基本类型数据，直接返回
            log.debug("数据类型为基本类型，无法应用筛选条件");
            return data;
        }
    }

    /**
     * 筛选数组数据
     */
    private static JSONArray filterArrayData(JSONArray arrayData, List<IntegrationFormFieldVO> filters) {
        JSONArray filteredArray = new JSONArray();

        for (Object item : arrayData) {
            if (matchesAllFilters(item, filters)) {
                filteredArray.add(item);
            }
        }

        log.debug("数组筛选完成，原始数量: {}, 筛选后数量: {}", arrayData.size(), filteredArray.size());
        return filteredArray;
    }

    /**
     * 筛选List数据
     */
    private static List<Object> filterListData(List<?> listData, List<IntegrationFormFieldVO> filters) {
        List<Object> filteredList = new ArrayList<>();

        for (Object item : listData) {
            if (matchesAllFilters(item, filters)) {
                filteredList.add(item);
            }
        }

        log.debug("List筛选完成，原始数量: {}, 筛选后数量: {}", listData.size(), filteredList.size());
        return filteredList;
    }

    /**
     * 筛选对象数据
     */
    private static Object filterObjectData(Object objectData, List<IntegrationFormFieldVO> filters) {
        // 对于对象数据，检查是否匹配所有筛选条件
        if (matchesAllFilters(objectData, filters)) {
            log.debug("对象数据匹配所有筛选条件，保留该对象");
            return objectData;
        } else {
            log.debug("对象数据不匹配筛选条件，返回null");
            return null;
        }
    }

    /**
     * 检查数据项是否匹配所有筛选条件（AND逻辑）
     */
    private static boolean matchesAllFilters(Object item, List<IntegrationFormFieldVO> filters) {
        if (item == null) {
            return false;
        }

        for (IntegrationFormFieldVO filter : filters) {
            if (!matchesSingleFilter(item, filter)) {
                return false; // 任何一个条件不匹配就返回false（AND逻辑）
            }
        }

        return true; // 所有条件都匹配
    }

    /**
     * 检查数据项是否匹配单个筛选条件
     */
    private static boolean matchesSingleFilter(Object item, IntegrationFormFieldVO filter) {
        try {
            // 根据location路径导航到目标数据位置
            Object targetData = navigateToLocation(item, filter.getLocation());

            // 从目标位置提取字段值
            Object actualValue = extractValueFromTarget(targetData, filter.getName(), filter.getLevel());

            if (actualValue == null) {
                log.debug("字段 {} 在位置 {} 未找到值，筛选条件不匹配", filter.getName(), filter.getLocation());
                return false;
            }

            // 获取期望值和比较操作符
            String expectedValue = filter.getValue();
            String operator = getComparisonOperator();

            // 执行值比较
            boolean matches = compareValues(actualValue, expectedValue, operator, filter.getColumnType());

            log.debug("字段 {} 筛选结果: 实际值={}, 期望值={}, 操作符={}, 匹配={}",
                     filter.getName(), actualValue, expectedValue, operator, matches);

            return matches;

        } catch (Exception e) {
            log.warn("筛选条件匹配时发生异常: {}, 字段: {}", e.getMessage(), filter.getName());
            return false;
        }
    }

    /**
     * 获取比较操作符，从type字段或使用默认的等于操作
     */
    private static String getComparisonOperator() {
        return "eq"; // 默认使用等于操作符
    }

    /**
     * 比较两个值
     */
    private static boolean compareValues(Object actualValue, String expectedValue, String operator, String columnType) {
        if (actualValue == null && expectedValue == null) {
            return "eq".equals(operator) || "gte".equals(operator) || "lte".equals(operator);
        }

        if (actualValue == null || expectedValue == null) {
            return "ne".equals(operator);
        }

        // 将实际值转换为字符串进行比较
        String actualStr = convertValueToString(actualValue);

        switch (operator) {
            case "eq":
                return compareEqual(actualStr, expectedValue, columnType);
            case "ne":
                return !compareEqual(actualStr, expectedValue, columnType);
            case "gt":
                return compareGreaterThan(actualStr, expectedValue, columnType);
            case "lt":
                return compareLessThan(actualStr, expectedValue, columnType);
            case "gte":
                return compareGreaterThan(actualStr, expectedValue, columnType) ||
                       compareEqual(actualStr, expectedValue, columnType);
            case "lte":
                return compareLessThan(actualStr, expectedValue, columnType) ||
                       compareEqual(actualStr, expectedValue, columnType);
            case "contains":
                return actualStr.toLowerCase().contains(expectedValue.toLowerCase());
            default:
                return compareEqual(actualStr, expectedValue, columnType);
        }
    }

    /**
     * 相等比较
     */
    private static boolean compareEqual(String actualValue, String expectedValue, String columnType) {
        if (actualValue == null && expectedValue == null) {
            return true;
        }
        if (actualValue == null || expectedValue == null) {
            return false;
        }

        // 根据字段类型进行类型化比较
        if (isNumericType(columnType)) {
            try {
                double actual = Double.parseDouble(actualValue);
                double expected = Double.parseDouble(expectedValue);
                return Math.abs(actual - expected) < 1e-9; // 浮点数比较
            } catch (NumberFormatException e) {
                log.debug("数值比较失败，使用字符串比较: actual={}, expected={}", actualValue, expectedValue);
                return actualValue.equals(expectedValue);
            }
        } else if (isBooleanType(columnType)) {
            return Boolean.parseBoolean(actualValue) == Boolean.parseBoolean(expectedValue);
        } else {
            // 字符串比较（忽略大小写）
            return actualValue.equalsIgnoreCase(expectedValue);
        }
    }

    /**
     * 大于比较
     */
    private static boolean compareGreaterThan(String actualValue, String expectedValue, String columnType) {
        if (isNumericType(columnType)) {
            try {
                double actual = Double.parseDouble(actualValue);
                double expected = Double.parseDouble(expectedValue);
                return actual > expected;
            } catch (NumberFormatException e) {
                log.debug("数值大于比较失败，使用字符串比较");
                return actualValue.compareTo(expectedValue) > 0;
            }
        } else {
            return actualValue.compareTo(expectedValue) > 0;
        }
    }

    /**
     * 小于比较
     */
    private static boolean compareLessThan(String actualValue, String expectedValue, String columnType) {
        if (isNumericType(columnType)) {
            try {
                double actual = Double.parseDouble(actualValue);
                double expected = Double.parseDouble(expectedValue);
                return actual < expected;
            } catch (NumberFormatException e) {
                log.debug("数值小于比较失败，使用字符串比较");
                return actualValue.compareTo(expectedValue) < 0;
            }
        } else {
            return actualValue.compareTo(expectedValue) < 0;
        }
    }

    /**
     * 判断是否为数值类型
     */
    private static boolean isNumericType(String columnType) {
        if (StringUtils.isBlank(columnType)) {
            return false;
        }
        String type = columnType.toUpperCase();
        return type.contains("INTEGER") || type.contains("LONG") || type.contains("DECIMAL") ||
               type.contains("DOUBLE") || type.contains("FLOAT") || type.contains("NUMBER");
    }

    /**
     * 判断是否为布尔类型
     */
    private static boolean isBooleanType(String columnType) {
        if (StringUtils.isBlank(columnType)) {
            return false;
        }
        String type = columnType.toUpperCase();
        return type.contains("BOOL");
    }

    /**
     * 提取需要保存的字段数据
     */
    private static List<IntegrationFormFieldVO> extractSaveFields(Object filteredData,
                                                                  List<InParam.ColumnParam> saveColumns,
                                                                  OutParam outParam) {
        List<IntegrationFormFieldVO> result = new ArrayList<>();

        // 使用LinkedHashMap保持插入顺序，用于去重
        Map<String, IntegrationFormFieldVO> fieldMap = new LinkedHashMap<>();

        for (InParam.ColumnParam saveColumn : saveColumns) {
            try {
                // 从响应数据中提取字段值
                Object fieldValue = extractFieldValue(filteredData, saveColumn, outParam);

                // 创建IntegrationFormFieldVO
                IntegrationFormFieldVO fieldVO = createFormFieldVO(saveColumn, fieldValue);

                // 使用字段名作为key进行去重，保留第一个遇到的字段
                if (!fieldMap.containsKey(saveColumn.getColumnName())) {
                    fieldMap.put(saveColumn.getColumnName(), fieldVO);
                }

            } catch (Exception e) {
                log.warn("提取字段 {} 失败: {}", saveColumn.getColumnName(), e.getMessage());
            }
        }

        // 转换为List，保持原始遍历顺序
        result.addAll(fieldMap.values());

        return result;
    }

    /**
     * 从响应数据中提取指定字段的值
     */
    private static Object extractFieldValue(Object responseData, InParam.ColumnParam saveColumn, OutParam outParam) {
        if (responseData == null || saveColumn == null) {
            return null;
        }

        String fieldName = saveColumn.getColumnName();
        String location = saveColumn.getLocation();
        Integer level = saveColumn.getLevel();

        // 根据location路径导航到目标位置
        Object targetData = navigateToLocation(responseData, location);

        if (targetData == null) {
            log.debug("无法根据location {} 找到目标数据", location);
            return null;
        }

        // 从目标位置提取字段值
        return extractValueFromTarget(targetData, fieldName, level);
    }

    /**
     * 根据location路径导航到目标数据位置
     */
    private static Object navigateToLocation(Object data, String location) {
        if (data == null) {
            return null;
        }

        // 如果location为空或者为"/"，表示根级别
        if (StringUtils.isBlank(location) || "/".equals(location)) {
            return data;
        }

        // 标准化location路径
        String normalizedLocation = normalizeLocation(location);
        if (StringUtils.isBlank(normalizedLocation)) {
            return data;
        }

        // 按"/"分割路径
        String[] pathParts = normalizedLocation.split("/");
        Object currentData = data;

        for (String pathPart : pathParts) {
            if (StringUtils.isBlank(pathPart)) {
                continue;
            }

            currentData = navigateOneLevel(currentData, pathPart);
            if (currentData == null) {
                return null;
            }
        }

        return currentData;
    }

    /**
     * 导航一个层级
     */
    private static Object navigateOneLevel(Object data, String key) {
        if (data == null || StringUtils.isBlank(key)) {
            return null;
        }

        if (data instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) data;
            return jsonObj.get(key);
        } else if (data instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) data;
            return map.get(key);
        } else if (data instanceof JSONArray) {
            // 如果是数组，尝试按索引访问或者访问数组中每个对象的指定字段
            JSONArray jsonArray = (JSONArray) data;
            try {
                int index = Integer.parseInt(key);
                if (index >= 0 && index < jsonArray.size()) {
                    return jsonArray.get(index);
                }
            } catch (NumberFormatException e) {
                // 不是数字索引，可能是要访问数组中对象的字段
                // 这种情况下返回第一个匹配的对象
                for (Object item : jsonArray) {
                    Object result = navigateOneLevel(item, key);
                    if (result != null) {
                        return result;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 从目标数据中提取指定字段的值
     */
    private static Object extractValueFromTarget(Object targetData, String fieldName, Integer level) {
        if (targetData == null || StringUtils.isBlank(fieldName)) {
            return null;
        }

        if (targetData instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) targetData;
            return jsonObj.get(fieldName);
        } else if (targetData instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) targetData;
            return map.get(fieldName);
        } else if (targetData instanceof JSONArray) {
            // 如果是数组，尝试从数组中的每个对象提取字段
            JSONArray jsonArray = (JSONArray) targetData;
            List<Object> values = new ArrayList<>();
            for (Object item : jsonArray) {
                Object value = extractValueFromTarget(item, fieldName, level);
                if (value != null) {
                    values.add(value);
                }
            }
            return values.isEmpty() ? null : (values.size() == 1 ? values.get(0) : values);
        }

        return null;
    }

    /**
     * 创建IntegrationFormFieldVO对象
     */
    private static IntegrationFormFieldVO createFormFieldVO(InParam.ColumnParam saveColumn, Object fieldValue) {
        IntegrationFormFieldVO fieldVO = new IntegrationFormFieldVO();

        // 设置基本字段信息
        fieldVO.setName(saveColumn.getColumnName());
        fieldVO.setLabel(StringUtils.isNotEmpty(saveColumn.getColumnComment()) ?
                        saveColumn.getColumnComment() : saveColumn.getColumnName());
        fieldVO.setColumnType(saveColumn.getColumnType());
        fieldVO.setLevel(saveColumn.getLevel());
        fieldVO.setLocation(saveColumn.getLocation());
        fieldVO.setNeedEncrypt(saveColumn.getNeedEncrypt());

        // 设置字段值
        fieldVO.setValue(convertValueToString(fieldValue));

        // 设置参数类型和数据类型
        fieldVO.setParamType(1); // 入参
        fieldVO.setDataType(2);  // 接口结果
        fieldVO.setRequired(false); // 来自接口结果的字段默认不必填

        // 根据字段类型设置输入框类型
        fieldVO.setType(getInputTypeByColumnType(saveColumn.getColumnType()));

        return fieldVO;
    }

    /**
     * 将值转换为字符串
     */
    private static String convertValueToString(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return (String) value;
        }

        if (value instanceof List || value instanceof JSONArray) {
            // 如果是列表或数组，转换为JSON字符串
            return JSONUtil.toJsonStr(value);
        }

        return String.valueOf(value);
    }

    /**
     * 根据字段类型获取输入框类型
     */
    private static String getInputTypeByColumnType(String columnType) {
        if (StringUtils.isBlank(columnType)) {
            return "text";
        }

        switch (columnType.toUpperCase()) {
            case "INTEGER":
            case "INT":
            case "LONG":
            case "BIGINT":
                return "number";
            case "DECIMAL":
            case "DOUBLE":
            case "FLOAT":
                return "number";
            case "BOOLEAN":
            case "BOOL":
                return "checkbox";
            case "DATE":
                return "date";
            case "DATETIME":
            case "TIMESTAMP":
                return "datetime";
            case "PASSWORD":
                return "password";
            case "EMAIL":
                return "email";
            case "URL":
                return "url";
            case "TEXTAREA":
                return "textarea";
            default:
                return "text";
        }
    }

    /**
     * 标准化location路径，去除开头和结尾的"/"
     */
    private static String normalizeLocation(String location) {
        if (StringUtils.isBlank(location)) {
            return "";
        }

        String normalized = location.trim();
        if (normalized.startsWith("/")) {
            normalized = normalized.substring(1);
        }
        if (normalized.endsWith("/")) {
            normalized = normalized.substring(0, normalized.length() - 1);
        }

        return normalized;
    }

    /**
     * 过滤显示字段，用于最终步骤的结果展示
     *
     * <p>当工作流中没有下一步时，不需要保存完整的响应数据，只需要保存配置的显示字段。</p>
     * <p>此方法会根据outParam中的显示字段配置，从响应数据中提取指定的字段，</p>
     * <p>返回适合前端表格显示的数据格式。</p>
     *
     * <p>显示字段识别规则：</p>
     * <ul>
     *   <li>从outParam.columnList中提取displayName不为空的字段</li>
     *   <li>使用columnName字段标识要提取的数据字段</li>
     *   <li>根据location路径定位到正确的数据位置</li>
     *   <li>返回适合前端表格显示的行数据格式</li>
     * </ul>
     *
     * <p>使用示例：</p>
     * <pre>
     * // 原始响应
     * String response = "{\"data\":[{\"id\":1,\"name\":\"John\",\"age\":25},{\"id\":2,\"name\":\"Jane\",\"age\":30}]}";
     *
     * // outParam配置显示字段：
     * // columnName="name", displayName="姓名", location="data"
     * // columnName="age", displayName="年龄", location="data"
     *
     * String filtered = filterDisplayFields(response, outParam);
     * // 结果: 适合前端表格显示的行数据格式
     * </pre>
     *
     * @param responseData 原始响应数据（JSON字符串）
     * @param outParam 响应格式配置，包含显示字段配置
     * @return 过滤后的JSON字符串，格式化为适合前端表格显示的数据
     */
    public static String filterDisplayFields(String responseData, OutParam outParam) {
        if (StringUtils.isBlank(responseData)) {
            log.warn("响应数据为空，返回空字符串");
            return "";
        }

        if (outParam == null || outParam.getColumnList() == null || outParam.getColumnList().isEmpty()) {
            log.debug("没有输出参数配置，返回原始响应数据");
            return responseData;
        }

        try {
            // 解析响应JSON
            Object responseObject = JSONUtil.parse(responseData);

            // 提取显示字段配置
            List<OutParam.ColumnParam> displayColumns = extractDisplayColumns(outParam);

            if (displayColumns.isEmpty()) {
                log.debug("没有配置显示字段，返回原始响应数据");
                return responseData;
            }

            log.debug("开始过滤显示字段，配置的显示字段数量: {}", displayColumns.size());

            // 提取表格行数据
            List<Map<String, Object>> tableRows = extractTableRowsFromData(responseObject, displayColumns);

            // 转换为JSON字符串
            String filteredJson = JSONUtil.toJsonStr(tableRows);

            log.info("显示字段过滤完成，原始数据长度: {}, 过滤后长度: {}, 提取行数: {}",
                    responseData.length(), filteredJson.length(), tableRows.size());

            return filteredJson;

        } catch (Exception e) {
            log.error("过滤显示字段时发生异常: {}, 返回原始响应数据", e.getMessage(), e);
            return responseData;
        }
    }

    /**
     * 从输出参数中提取显示字段配置
     */
    private static List<OutParam.ColumnParam> extractDisplayColumns(OutParam outParam) {
        return outParam.getColumnList().stream()
                .filter(column -> StringUtils.isNotBlank(column.getDisplayName())
                        && StringUtils.isNotBlank(column.getColumnName()))
                .collect(Collectors.toList());
    }

    /**
     * 从数据中提取表格行数据
     */
    private static List<Map<String, Object>> extractTableRowsFromData(Object data, List<OutParam.ColumnParam> displayColumns) {
        List<Map<String, Object>> tableRows = new ArrayList<>();

        if (data == null || displayColumns.isEmpty()) {
            return tableRows;
        }

        // 根据数据类型应用不同的提取策略
        if (data instanceof JSONArray) {
            extractRowsFromArray((JSONArray) data, displayColumns, tableRows);
        } else if (data instanceof List) {
            extractRowsFromList((List<?>) data, displayColumns, tableRows);
        } else if (data instanceof JSONObject || data instanceof Map) {
            // 单个对象，提取为一行数据
            Map<String, Object> row = extractSingleRow(data, displayColumns);
            if (!row.isEmpty()) {
                tableRows.add(row);
            }
        } else {
            log.debug("数据类型为基本类型，无法提取表格行数据");
        }

        return tableRows;
    }

    /**
     * 从JSONArray中提取表格行数据
     */
    private static void extractRowsFromArray(JSONArray jsonArray, List<OutParam.ColumnParam> displayColumns, List<Map<String, Object>> tableRows) {
        for (Object item : jsonArray) {
            Map<String, Object> row = extractSingleRow(item, displayColumns);
            if (!row.isEmpty()) {
                tableRows.add(row);
            }
        }
    }

    /**
     * 从List中提取表格行数据
     */
    private static void extractRowsFromList(List<?> list, List<OutParam.ColumnParam> displayColumns, List<Map<String, Object>> tableRows) {
        for (Object item : list) {
            Map<String, Object> row = extractSingleRow(item, displayColumns);
            if (!row.isEmpty()) {
                tableRows.add(row);
            }
        }
    }

    /**
     * 从单个数据对象中提取一行表格数据
     */
    private static Map<String, Object> extractSingleRow(Object dataItem, List<OutParam.ColumnParam> displayColumns) {
        Map<String, Object> row = new LinkedHashMap<>();

        for (OutParam.ColumnParam column : displayColumns) {
            try {
                // 根据location路径导航到目标数据位置
                Object targetData = navigateToLocation(dataItem, column.getLocation());

                // 从目标位置提取字段值
                Object fieldValue = extractValueFromTarget(targetData, column.getColumnName(), column.getLevel());

                // 使用displayName作为key，字段值作为value
                String displayKey = column.getDisplayName();
                String displayValue = convertValueToString(fieldValue);

                row.put(displayKey, displayValue);

                log.debug("提取字段: {} -> {} = {}", column.getColumnName(), displayKey, displayValue);

            } catch (Exception e) {
                log.warn("提取字段 {} 失败: {}", column.getColumnName(), e.getMessage());
                // 如果字段提取失败，设置为null或空字符串
                row.put(column.getDisplayName(), null);
            }
        }

        return row;
    }

}
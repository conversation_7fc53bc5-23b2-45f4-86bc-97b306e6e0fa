package com.dcas.common.utils.params;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ResponseConverter {

    /**
     * 将HTTP接口响应内容和响应格式配置转换为IntegrationFormFieldVO列表
     *
     * @param response HTTP接口响应内容
     * @param outParam 响应格式配置,记录了响应内容字段的字段类型以及层级关系等
     * @param filterParams 配置的筛选条件
     * @param saveColumns 配置的保存字段
     * @return IntegrationFormFieldVO列表
     */
    public static List<IntegrationFormFieldVO> convertToFormFields(String response, OutParam outParam,
                                                                   List<IntegrationFormFieldVO> filterParams,
                                                                   List<InParam.ColumnParam> saveColumns) {
        if (StringUtils.isBlank(response) || outParam == null || saveColumns == null || saveColumns.isEmpty()) {
            log.warn("响应数据为空或配置参数缺失，返回空列表");
            return new ArrayList<>();
        }

        try {
            // 解析响应JSON
            Object responseData = JSONUtil.parse(response);

            // 应用筛选条件过滤响应数据
            Object filteredData = applyFilterConditions(responseData, filterParams, outParam);

            // 提取需要保存的字段数据
            List<IntegrationFormFieldVO> result = extractSaveFields(filteredData, saveColumns, outParam);

            log.info("成功转换响应数据，提取到 {} 个字段", result.size());
            return result;

        } catch (Exception e) {
            log.error("转换响应数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 应用筛选条件过滤响应数据
     */
    private static Object applyFilterConditions(Object responseData, List<IntegrationFormFieldVO> filterParams, OutParam outParam) {
        if (filterParams == null || filterParams.isEmpty()) {
            return responseData;
        }

        // 如果没有筛选条件，直接返回原数据
        List<IntegrationFormFieldVO> actualFilters = filterParams.stream()
                .filter(param -> param.getParamType() != null && param.getParamType() == 2)
                .collect(Collectors.toList());

        if (actualFilters.isEmpty()) {
            return responseData;
        }

        // TODO: 实现具体的筛选逻辑
        // 这里可以根据filterParams中的条件对responseData进行筛选
        // 当前先返回原数据，后续可以根据具体的筛选需求进行扩展
        log.debug("应用了 {} 个筛选条件", actualFilters.size());
        return responseData;
    }

    /**
     * 提取需要保存的字段数据
     */
    private static List<IntegrationFormFieldVO> extractSaveFields(Object filteredData,
                                                                  List<InParam.ColumnParam> saveColumns,
                                                                  OutParam outParam) {
        List<IntegrationFormFieldVO> result = new ArrayList<>();

        // 使用LinkedHashMap保持插入顺序，用于去重
        Map<String, IntegrationFormFieldVO> fieldMap = new LinkedHashMap<>();

        for (InParam.ColumnParam saveColumn : saveColumns) {
            try {
                // 从响应数据中提取字段值
                Object fieldValue = extractFieldValue(filteredData, saveColumn, outParam);

                // 创建IntegrationFormFieldVO
                IntegrationFormFieldVO fieldVO = createFormFieldVO(saveColumn, fieldValue);

                // 使用字段名作为key进行去重，保留第一个遇到的字段
                if (!fieldMap.containsKey(saveColumn.getColumnName())) {
                    fieldMap.put(saveColumn.getColumnName(), fieldVO);
                }

            } catch (Exception e) {
                log.warn("提取字段 {} 失败: {}", saveColumn.getColumnName(), e.getMessage());
            }
        }

        // 转换为List，保持原始遍历顺序
        result.addAll(fieldMap.values());

        return result;
    }

    /**
     * 从响应数据中提取指定字段的值
     */
    private static Object extractFieldValue(Object responseData, InParam.ColumnParam saveColumn, OutParam outParam) {
        if (responseData == null || saveColumn == null) {
            return null;
        }

        String fieldName = saveColumn.getColumnName();
        String location = saveColumn.getLocation();
        Integer level = saveColumn.getLevel();

        // 根据location路径导航到目标位置
        Object targetData = navigateToLocation(responseData, location);

        if (targetData == null) {
            log.debug("无法根据location {} 找到目标数据", location);
            return null;
        }

        // 从目标位置提取字段值
        return extractValueFromTarget(targetData, fieldName, level);
    }

    /**
     * 根据location路径导航到目标数据位置
     */
    private static Object navigateToLocation(Object data, String location) {
        if (data == null) {
            return null;
        }

        // 如果location为空或者为"/"，表示根级别
        if (StringUtils.isBlank(location) || "/".equals(location)) {
            return data;
        }

        // 标准化location路径
        String normalizedLocation = normalizeLocation(location);
        if (StringUtils.isBlank(normalizedLocation)) {
            return data;
        }

        // 按"/"分割路径
        String[] pathParts = normalizedLocation.split("/");
        Object currentData = data;

        for (String pathPart : pathParts) {
            if (StringUtils.isBlank(pathPart)) {
                continue;
            }

            currentData = navigateOneLevel(currentData, pathPart);
            if (currentData == null) {
                return null;
            }
        }

        return currentData;
    }

    /**
     * 导航一个层级
     */
    private static Object navigateOneLevel(Object data, String key) {
        if (data == null || StringUtils.isBlank(key)) {
            return null;
        }

        if (data instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) data;
            return jsonObj.get(key);
        } else if (data instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) data;
            return map.get(key);
        } else if (data instanceof JSONArray) {
            // 如果是数组，尝试按索引访问或者访问数组中每个对象的指定字段
            JSONArray jsonArray = (JSONArray) data;
            try {
                int index = Integer.parseInt(key);
                if (index >= 0 && index < jsonArray.size()) {
                    return jsonArray.get(index);
                }
            } catch (NumberFormatException e) {
                // 不是数字索引，可能是要访问数组中对象的字段
                // 这种情况下返回第一个匹配的对象
                for (Object item : jsonArray) {
                    Object result = navigateOneLevel(item, key);
                    if (result != null) {
                        return result;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 从目标数据中提取指定字段的值
     */
    private static Object extractValueFromTarget(Object targetData, String fieldName, Integer level) {
        if (targetData == null || StringUtils.isBlank(fieldName)) {
            return null;
        }

        if (targetData instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) targetData;
            return jsonObj.get(fieldName);
        } else if (targetData instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) targetData;
            return map.get(fieldName);
        } else if (targetData instanceof JSONArray) {
            // 如果是数组，尝试从数组中的每个对象提取字段
            JSONArray jsonArray = (JSONArray) targetData;
            List<Object> values = new ArrayList<>();
            for (Object item : jsonArray) {
                Object value = extractValueFromTarget(item, fieldName, level);
                if (value != null) {
                    values.add(value);
                }
            }
            return values.isEmpty() ? null : (values.size() == 1 ? values.get(0) : values);
        }

        return null;
    }

    /**
     * 创建IntegrationFormFieldVO对象
     */
    private static IntegrationFormFieldVO createFormFieldVO(InParam.ColumnParam saveColumn, Object fieldValue) {
        IntegrationFormFieldVO fieldVO = new IntegrationFormFieldVO();

        // 设置基本字段信息
        fieldVO.setName(saveColumn.getColumnName());
        fieldVO.setLabel(StringUtils.isNotEmpty(saveColumn.getColumnComment()) ?
                        saveColumn.getColumnComment() : saveColumn.getColumnName());
        fieldVO.setColumnType(saveColumn.getColumnType());
        fieldVO.setLevel(saveColumn.getLevel());
        fieldVO.setLocation(saveColumn.getLocation());
        fieldVO.setNeedEncrypt(saveColumn.getNeedEncrypt());

        // 设置字段值
        fieldVO.setValue(convertValueToString(fieldValue));

        // 设置参数类型和数据类型
        fieldVO.setParamType(1); // 入参
        fieldVO.setDataType(2);  // 接口结果
        fieldVO.setRequired(false); // 来自接口结果的字段默认不必填

        // 根据字段类型设置输入框类型
        fieldVO.setType(getInputTypeByColumnType(saveColumn.getColumnType()));

        return fieldVO;
    }

    /**
     * 将值转换为字符串
     */
    private static String convertValueToString(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return (String) value;
        }

        if (value instanceof List || value instanceof JSONArray) {
            // 如果是列表或数组，转换为JSON字符串
            return JSONUtil.toJsonStr(value);
        }

        return String.valueOf(value);
    }

    /**
     * 根据字段类型获取输入框类型
     */
    private static String getInputTypeByColumnType(String columnType) {
        if (StringUtils.isBlank(columnType)) {
            return "text";
        }

        switch (columnType.toUpperCase()) {
            case "INTEGER":
            case "INT":
            case "LONG":
            case "BIGINT":
                return "number";
            case "DECIMAL":
            case "DOUBLE":
            case "FLOAT":
                return "number";
            case "BOOLEAN":
            case "BOOL":
                return "checkbox";
            case "DATE":
                return "date";
            case "DATETIME":
            case "TIMESTAMP":
                return "datetime";
            case "PASSWORD":
                return "password";
            case "EMAIL":
                return "email";
            case "URL":
                return "url";
            case "TEXTAREA":
                return "textarea";
            default:
                return "text";
        }
    }

    /**
     * 标准化location路径，去除开头和结尾的"/"
     */
    private static String normalizeLocation(String location) {
        if (StringUtils.isBlank(location)) {
            return "";
        }

        String normalized = location.trim();
        if (normalized.startsWith("/")) {
            normalized = normalized.substring(1);
        }
        if (normalized.endsWith("/")) {
            normalized = normalized.substring(0, normalized.length() - 1);
        }

        return normalized;
    }
}
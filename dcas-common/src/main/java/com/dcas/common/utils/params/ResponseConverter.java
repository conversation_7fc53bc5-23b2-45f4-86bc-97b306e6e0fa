package com.dcas.common.utils.params;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class ResponseConverter {

    /**
     * 将HTTP接口响应内容和响应格式配置转换为IntegrationFormFieldVO列表
     *
     * @param response HTTP接口响应内容
     * @param outParam 响应格式配置,记录了响应内容字段的字段类型以及层级关系等
     * @param filterParams 配置的筛选条件
     * @param saveColumns 配置的保存字段
     * @return IntegrationFormFieldVO列表
     */
    public static List<IntegrationFormFieldVO> convertToFormFields(String response, OutParam outParam, List<IntegrationFormFieldVO> filterParams, List<InParam.ColumnParam> saveColumns) {
        return null;
    }
}
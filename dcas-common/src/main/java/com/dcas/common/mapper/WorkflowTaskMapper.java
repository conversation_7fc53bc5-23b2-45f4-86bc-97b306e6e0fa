package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.WorkflowTask;
import com.dcas.common.model.req.WorkflowTaskReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流任务Mapper接口
 * 
 * <AUTHOR>
 */
public interface WorkflowTaskMapper extends BaseMapper<WorkflowTask> {
    
    /**
     * 查询工作流任务
     *
     * @param taskId 工作流任务主键
     * @return 工作流任务
     */
    WorkflowTask selectWorkflowTaskByTaskId(Long taskId);

    /**
     * 查询工作流任务列表
     *
     * @param req 工作流任务
     * @return 工作流任务集合
     */
    List<WorkflowTask> selectWorkflowTaskList(@Param("req") WorkflowTaskReq req);

    /**
     * 新增工作流任务
     *
     * @param workflowTask 工作流任务
     * @return 结果
     */
    int insertWorkflowTask(WorkflowTask workflowTask);

    /**
     * 修改工作流任务
     *
     * @param workflowTask 工作流任务
     * @return 结果
     */
    int updateWorkflowTask(WorkflowTask workflowTask);

    /**
     * 删除工作流任务
     *
     * @param taskId 工作流任务主键
     * @return 结果
     */
    int deleteWorkflowTaskByTaskId(Long taskId);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @param updateBy 更新者
     * @return 结果
     */
    int updateWorkflowTaskStatus(@Param("taskId") Long taskId,
                                       @Param("status") Integer status,
                                       @Param("updateBy") String updateBy);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param currentStep 当前步骤
     * @param progressPercentage 进度百分比
     * @param updateBy 更新者
     * @return 结果
     */
    int updateWorkflowTaskProgress(@Param("taskId") Long taskId,
                                         @Param("currentStep") Integer currentStep,
                                         @Param("progressPercentage") Integer progressPercentage,
                                         @Param("updateBy") String updateBy);

    /**
     * 更新任务执行时间
     *
     * @param taskId 任务ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param executionDuration 执行时长
     * @param updateBy 更新者
     * @return 结果
     */
    int updateWorkflowTaskExecutionTime(@Param("taskId") Long taskId,
                                              @Param("startTime") java.util.Date startTime,
                                              @Param("endTime") java.util.Date endTime,
                                              @Param("executionDuration") Long executionDuration,
                                              @Param("updateBy") String updateBy);

    /**
     * 更新任务错误信息
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @param updateBy 更新者
     * @return 结果
     */
    int updateWorkflowTaskError(@Param("taskId") Long taskId,
                                      @Param("errorMessage") String errorMessage,
                                      @Param("updateBy") String updateBy);
}

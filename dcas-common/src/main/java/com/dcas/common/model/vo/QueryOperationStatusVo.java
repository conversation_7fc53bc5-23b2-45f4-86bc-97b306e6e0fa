package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询作业状态返参
 */
@ApiModel
@Data
public class QueryOperationStatusVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "评估作业")
    private WorkOverview access;

    @ApiModelProperty(value = "检查作业")
    private WorkOverview review;

    @ApiModelProperty(value = "创建作业数量")
    private Integer createNum;

    @ApiModelProperty(value = "结项作业数量")
    private Integer completeNum;

    @Data
    public static class WorkOverview {
        @ApiModelProperty(value = "作业类型")
        private String name;

        @ApiModelProperty(value = "创建作业数量")
        private Integer create;

        @ApiModelProperty(value = "待启动")
        private Integer onWaiting;

        @ApiModelProperty(value = "执行中")
        private Integer onGoing;

        @ApiModelProperty(value = "复核中")
        private Integer onReview;

        @ApiModelProperty(value = "已结项")
        private Integer completed;
    }
}

package com.dcas.common.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/12 10:27
 * @since 1.0.0
 */
@Data
public class ProductInfoVO {

    private Long id;

    /**
     * 集成ID
     */
    @ApiModelProperty("集成ID")
    private Long releaseId;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("产品地址")
    private String url;

    /**
     * 产品能力
     */
    @ApiModelProperty("产品能力")
    private String capability;

    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    private String productName;

    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    private String company;

    /**
     * 产品logo
     */
    @ApiModelProperty("产品logo")
    private String logo;

    /**
     * 集成状态
     */
    @ApiModelProperty("集成状态")
    private String status;

    @ApiModelProperty("版本号")
    private Integer version;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;

    /**
     * API接口列表
     */
    @ApiModelProperty("API接口参数列表")
    private List<IntegrationFormFieldVO> params;
}

package com.dcas.common.model.req;

import com.dcas.common.model.param.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/16 9:26
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowTaskReq extends PageQuery {
    private static final long serialVersionUID = 1450502766660672909L;

    @ApiModelProperty(value = "作业名称")
    private String name;

    @ApiModelProperty(value = "分类分级产品ID")
    private String productName;

    @ApiModelProperty(value = "任务类型 1-能力验证 2-结果验证")
    private Integer taskType;

    @ApiModelProperty(value = "任务状态 0-待执行, 1-执行中, 2-已完成, 3-失败, 4-已终止")
    private Integer status;

    @ApiModelProperty(value = "能力验证结果")
    private String verifyResult;
}

package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 报告图表配置详情
 *
 * <AUTHOR>
 * @date 2024/02/27 11:13
 **/
@Data
public class ReportConfigDetailDTO extends ReportConfigBaseDTO{

    @ApiModelProperty("多指标配置，供资产统计图使用")
    private List<ReportConfigBaseDTO> baseList;

    @ApiModelProperty("数据范围配置，供综合计算值图表使用")
    private List<ReportConfigDataScopeDTO> dataScopeList;

    @ApiModelProperty("y轴名称")
    private String yAxis;

    @ApiModelProperty("是否选择框：true-是，false-手写输入框")
    private Boolean isCheckBox;

}

package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/1/19 16:51
 * @since 1.7.0
 */
@Data
public class DetermineAssetDTO {

    @ApiModelProperty(value = "资产id,新增时不传,修改时必传")
    private Integer id;

    @ApiModelProperty(value = "作业id")
    @NotBlank(message = "作业id不能为空")
    private String operationId;

    @ApiModelProperty(value = "分类id")
    @NotNull(message = "分类id不能为空")
    private Integer typeId;

    @ApiModelProperty(value = "资产名称")
    @NotBlank(message = "资产名称不能为空")
    private String name;

    @ApiModelProperty(value = "资产描述")
    @NotBlank(message = "资产描述不能为空")
    private String introduce;

    @ApiModelProperty(value = "资产敏感等级")
    @NotNull(message = "资产敏感等级不能为空")
    private Integer sensitiveLevel;

    @ApiModelProperty(value = "系统id")
    @NotNull(message = "系统id不能为空")
    private Long systemId;

    @ApiModelProperty(value = "数据标识 GENERAL-一般数据，CORE-核心数据，IMPORTANT-重要数据")
    @NotBlank(message = "数据标识不能为空")
    private String dataTag;
}

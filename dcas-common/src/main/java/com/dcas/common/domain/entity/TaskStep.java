package com.dcas.common.domain.entity;

import com.dcas.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 任务步骤实体类
 * 
 * <AUTHOR>
 */
@Data
public class TaskStep implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 步骤ID */
    private Long id;

    /** 任务ID */
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    @ApiModelProperty(value = "接口ID")
    private Long interfaceId;

    /** 步骤序号 */
    @NotNull(message = "步骤序号不能为空")
    private Integer stepOrder;

    /** 步骤名称 */
    @NotBlank(message = "步骤名称不能为空")
    @Size(min = 0, max = 100, message = "步骤名称不能超过100个字符")
    private String stepName;

    /** 步骤描述 */
    @Size(min = 0, max = 500, message = "步骤描述不能超过500个字符")
    private String stepDescription;

    @ApiModelProperty(value = "步骤状态：0-待执行, 1-执行中, 2-已完成, 3-失败")
    private Integer status;

    /** API端点URL */
    @NotBlank(message = "API端点不能为空")
    @Size(min = 0, max = 500, message = "API端点不能超过500个字符")
    private String apiEndpoint;

    /** HTTP方法：GET, POST, PUT, DELETE */
    @NotBlank(message = "HTTP方法不能为空")
    private String httpMethod;

    /** 请求头配置JSON */
    private String requestHeaders;

    /** 请求参数配置JSON */
    private String requestParams;

    /** 响应格式JSON */
    private String responseFormat;

    /** 超时时间（秒） */
    @Excel(name = "超时时间(秒)")
    private Integer timeoutSeconds;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** 最大重试次数 */
    @Excel(name = "最大重试次数")
    private Integer maxRetries;

    /** 是否必须成功：0-否, 1-是 */
    private Integer required;

    /** 失败时是否继续：0-否, 1-是 */
    private Integer continueOnFailure;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 执行时长（毫秒） */
    private Long executionDuration;

    /** 响应状态码 */
    private Integer responseStatus;

    /** 响应数据 */
    private String responseData;

    /** 错误信息 */
    private String errorMessage;

    /** 输出数据（传递给下一步的数据） */
    private String outputData;

    /** 创建时间 */
    private Date createTime;

    /** 创建者 */
    private String createBy;
}

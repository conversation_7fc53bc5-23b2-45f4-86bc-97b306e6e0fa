<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.WorkflowTaskMapper">

    <resultMap type="WorkflowTask" id="WorkflowTaskResult">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="capability" column="capability"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="taskType" column="task_type"/>
        <result property="taskConfig" column="task_config"/>
        <result property="currentStep" column="current_step"/>
        <result property="totalSteps" column="total_steps"/>
        <result property="progressPercentage" column="progress_percentage"/>
        <result property="errorMessage" column="error_message"/>
        <result property="executionResult" column="execution_result"/>
        <result property="verifyResult" column="verify_result"/>
        <result property="status" column="status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="executionDuration" column="execution_duration"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectWorkflowTaskVo">
        select id, name, capability, product_id, product_name, task_type, task_config,
               current_step, total_steps, progress_percentage, error_message, execution_result, verify_result,
               status, start_time, end_time, execution_duration,
               create_time, create_by, update_time, update_by
        from workflow_task
    </sql>

    <select id="selectWorkflowTaskList" parameterType="com.dcas.common.model.req.WorkflowTaskReq" resultMap="WorkflowTaskResult">
        <include refid="selectWorkflowTaskVo"/>
        <where>
            <if test="req.name != null and req.name != ''">
                AND name like concat('%', #{req.name}, '%')
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND product_name like concat('%', #{req.productName}, '%')
            </if>
            <if test="req.status != null">
                AND status = #{req.status}
            </if>
            <if test="req.taskType != null">
                AND task_type = #{req.taskType}
            </if>
            <if test="req.verifyResult != null and req.verifyResult != ''">
                AND verify_result like concat('%', #{req.verifyResult}, '%')
            </if>
        </where>
        order by task_id desc
    </select>

    <select id="selectWorkflowTaskByTaskId" parameterType="Long" resultMap="WorkflowTaskResult">
        <include refid="selectWorkflowTaskVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertWorkflowTask" parameterType="WorkflowTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into workflow_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="taskDescription != null">task_description,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="taskType != null">task_type,</if>
            <if test="priority != null">priority,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="executionDuration != null">execution_duration,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="totalSteps != null">total_steps,</if>
            <if test="progressPercentage != null">progress_percentage,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="executionResult != null">execution_result,</if>
            <if test="taskConfig != null">task_config,</if>
            <if test="restartable != null">restartable,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="maxRetries != null">max_retries,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="taskDescription != null">#{taskDescription},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="priority != null">#{priority},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="executionDuration != null">#{executionDuration},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="totalSteps != null">#{totalSteps},</if>
            <if test="progressPercentage != null">#{progressPercentage},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="executionResult != null">#{executionResult},</if>
            <if test="taskConfig != null">#{taskConfig},</if>
            <if test="restartable != null">#{restartable},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="maxRetries != null">#{maxRetries},</if>
            current_timestamp
        </trim>
    </insert>

    <update id="updateWorkflowTask" parameterType="WorkflowTask">
        update workflow_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="update_time != null">update_time = #{updateTime}</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteWorkflowTaskByTaskId" parameterType="Long">
        delete from workflow_task where task_id = #{taskId}
    </delete>

    <!-- 更新任务状态 -->
    <update id="updateWorkflowTaskStatus">
        update workflow_task
        set status = #{status},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where task_id = #{taskId}
    </update>

    <!-- 更新任务进度 -->
    <update id="updateWorkflowTaskProgress">
        update workflow_task
        set current_step = #{currentStep},
            progress_percentage = #{progressPercentage},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where task_id = #{taskId}
    </update>

    <!-- 更新任务执行时间 -->
    <update id="updateWorkflowTaskExecutionTime">
        update workflow_task
        set start_time = #{startTime},
            end_time = #{endTime},
            execution_duration = #{executionDuration},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where task_id = #{taskId}
    </update>

    <!-- 更新任务错误信息 -->
    <update id="updateWorkflowTaskError">
        update workflow_task
        set error_message = #{errorMessage},
            update_time = current_timestamp,
            update_by = #{updateBy}
        where task_id = #{taskId}
    </update>

</mapper>

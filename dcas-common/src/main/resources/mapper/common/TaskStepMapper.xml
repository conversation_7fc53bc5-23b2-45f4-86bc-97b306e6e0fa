<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.TaskStepMapper">

    <resultMap type="TaskStep" id="TaskStepResult">
        <id property="stepId" column="step_id"/>
        <result property="taskId" column="task_id"/>
        <result property="stepOrder" column="step_order"/>
        <result property="stepName" column="step_name"/>
        <result property="stepDescription" column="step_description"/>
        <result property="status" column="status"/>
        <result property="apiEndpoint" column="api_endpoint"/>
        <result property="httpMethod" column="http_method"/>
        <result property="requestHeaders" column="request_headers"/>
        <result property="requestParams" column="request_params"/>
        <result property="requestBody" column="request_body"/>
        <result property="timeoutSeconds" column="timeout_seconds"/>
        <result property="retryCount" column="retry_count"/>
        <result property="maxRetries" column="max_retries"/>
        <result property="required" column="required"/>
        <result property="continueOnFailure" column="continue_on_failure"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="executionDuration" column="execution_duration"/>
        <result property="responseStatus" column="response_status"/>
        <result property="responseData" column="response_data"/>
        <result property="errorMessage" column="error_message"/>
        <result property="outputData" column="output_data"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
    </resultMap>

    <sql id="selectTaskStepVo">
        select step_id, task_id, step_order, step_name, step_description, status,
               api_endpoint, http_method, request_headers, request_params, request_body,
               timeout_seconds, retry_count, max_retries, required, continue_on_failure,
               start_time, end_time, execution_duration, response_status, response_data,
               error_message, output_data, create_time, create_by
        from task_step
    </sql>

    <select id="selectTaskStepList" parameterType="TaskStep" resultMap="TaskStepResult">
        <include refid="selectTaskStepVo"/>
        <where>
            <if test="stepId != null">
                AND step_id = #{stepId}
            </if>
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
            <if test="stepName != null and stepName != ''">
                AND step_name like concat('%', #{stepName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by task_id, step_order
    </select>

    <select id="selectTaskStepByStepId" parameterType="Long" resultMap="TaskStepResult">
        <include refid="selectTaskStepVo"/>
        where step_id = #{stepId}
    </select>

    <select id="selectTaskStepByTaskId" parameterType="Long" resultMap="TaskStepResult">
        <include refid="selectTaskStepVo"/>
        where task_id = #{taskId}
        order by step_order
    </select>

    <select id="selectTaskStepByTaskIdAndOrder" resultMap="TaskStepResult">
        <include refid="selectTaskStepVo"/>
        where task_id = #{taskId} and step_order = #{stepOrder}
    </select>

    <insert id="insertTaskStep" parameterType="TaskStep" useGeneratedKeys="true" keyProperty="stepId">
        insert into task_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="stepOrder != null">step_order,</if>
            <if test="stepName != null and stepName != ''">step_name,</if>
            <if test="stepDescription != null">step_description,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="apiEndpoint != null and apiEndpoint != ''">api_endpoint,</if>
            <if test="httpMethod != null and httpMethod != ''">http_method,</if>
            <if test="requestHeaders != null">request_headers,</if>
            <if test="requestParams != null">request_params,</if>
            <if test="requestBody != null">request_body,</if>
            <if test="timeoutSeconds != null">timeout_seconds,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="maxRetries != null">max_retries,</if>
            <if test="required != null">required,</if>
            <if test="continueOnFailure != null">continue_on_failure,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="executionDuration != null">execution_duration,</if>
            <if test="responseStatus != null">response_status,</if>
            <if test="responseData != null">response_data,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="outputData != null">output_data,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="stepOrder != null">#{stepOrder},</if>
            <if test="stepName != null and stepName != ''">#{stepName},</if>
            <if test="stepDescription != null">#{stepDescription},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="apiEndpoint != null and apiEndpoint != ''">#{apiEndpoint},</if>
            <if test="httpMethod != null and httpMethod != ''">#{httpMethod},</if>
            <if test="requestHeaders != null">#{requestHeaders},</if>
            <if test="requestParams != null">#{requestParams},</if>
            <if test="requestBody != null">#{requestBody},</if>
            <if test="timeoutSeconds != null">#{timeoutSeconds},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="maxRetries != null">#{maxRetries},</if>
            <if test="required != null">#{required},</if>
            <if test="continueOnFailure != null">#{continueOnFailure},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="executionDuration != null">#{executionDuration},</if>
            <if test="responseStatus != null">#{responseStatus},</if>
            <if test="responseData != null">#{responseData},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="outputData != null">#{outputData},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            current_timestamp
        </trim>
    </insert>

    <update id="updateTaskStep" parameterType="TaskStep">
        update task_step
        <trim prefix="SET" suffixOverrides=",">
            <if test="stepOrder != null">step_order = #{stepOrder},</if>
            <if test="stepName != null and stepName != ''">step_name = #{stepName},</if>
            <if test="stepDescription != null">step_description = #{stepDescription},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="apiEndpoint != null and apiEndpoint != ''">api_endpoint = #{apiEndpoint},</if>
            <if test="httpMethod != null and httpMethod != ''">http_method = #{httpMethod},</if>
            <if test="requestHeaders != null">request_headers = #{requestHeaders},</if>
            <if test="requestParams != null">request_params = #{requestParams},</if>
            <if test="requestBody != null">request_body = #{requestBody},</if>
            <if test="timeoutSeconds != null">timeout_seconds = #{timeoutSeconds},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="maxRetries != null">max_retries = #{maxRetries},</if>
            <if test="required != null">required = #{required},</if>
            <if test="continueOnFailure != null">continue_on_failure = #{continueOnFailure},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="executionDuration != null">execution_duration = #{executionDuration},</if>
            <if test="responseStatus != null">response_status = #{responseStatus},</if>
            <if test="responseData != null">response_data = #{responseData},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="outputData != null">output_data = #{outputData},</if>
        </trim>
        where step_id = #{stepId}
    </update>

    <delete id="deleteTaskStepByStepId" parameterType="Long">
        delete from task_step where step_id = #{stepId}
    </delete>

    <delete id="deleteTaskStepByStepIds" parameterType="Long">
        delete from task_step where step_id in
        <foreach collection="array" item="stepId" open="(" separator="," close=")">
            #{stepId}
        </foreach>
    </delete>

    <delete id="deleteTaskStepByTaskId" parameterType="Long">
        delete from task_step where task_id = #{taskId}
    </delete>

    <!-- 更新步骤状态 -->
    <update id="updateTaskStepStatus">
        update task_step
        set status = #{status}
        where step_id = #{stepId}
    </update>

    <!-- 更新步骤执行时间 -->
    <update id="updateTaskStepExecutionTime">
        update task_step
        set start_time = #{startTime},
            end_time = #{endTime},
            execution_duration = #{executionDuration}
        where step_id = #{stepId}
    </update>

    <!-- 更新步骤响应信息 -->
    <update id="updateTaskStepResponse">
        update task_step
        set response_status = #{responseStatus},
            response_data = #{responseData}
        where step_id = #{stepId}
    </update>

    <!-- 更新步骤错误信息 -->
    <update id="updateTaskStepError">
        update task_step
        set error_message = #{errorMessage}
        where step_id = #{stepId}
    </update>

    <!-- 更新步骤输出数据 -->
    <update id="updateTaskStepOutput">
        update task_step
        set output_data = #{outputData}
        where step_id = #{stepId}
    </update>

    <!-- 增加步骤重试次数 -->
    <update id="incrementStepRetryCount">
        update task_step
        set retry_count = retry_count + 1
        where step_id = #{stepId}
    </update>

    <!-- 根据状态查询步骤列表 -->
    <select id="selectTaskStepByTaskIdAndStatus" resultMap="TaskStepResult">
        <include refid="selectTaskStepVo"/>
        where task_id = #{taskId} and status = #{status}
        order by step_order
    </select>

    <!-- 查询任务的下一个待执行步骤 -->
    <select id="selectNextPendingStep" parameterType="Long" resultMap="TaskStepResult">
        <include refid="selectTaskStepVo"/>
        where task_id = #{taskId} and status = 'PENDING'
        order by step_order
        limit 1
    </select>

    <!-- 统计任务步骤数量 -->
    <select id="countTaskStepsByTaskId" parameterType="Long" resultType="int">
        select count(*) from task_step where task_id = #{taskId}
    </select>

    <!-- 统计任务中各状态的步骤数量 -->
    <select id="countTaskStepsByTaskIdAndStatus" resultType="int">
        select count(*) from task_step where task_id = #{taskId} and status = #{status}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.ThreatTreeMapper">

    <sql id="selectTreeVo">
        select d.tree_id,
               d.parent_id,
               d.type,
               d.tree_name,
               d.tree_code,
               d.order_num,
               d.status,
               d.remark,
               d.del_flag,
               d.create_by,
               d.create_time
        from threat_tree d
    </sql>

    <select id="selectTreeListNew" resultType="com.dcas.common.domain.entity.ThreatTree">
        <include refid="selectTreeVo"/>
        where d.del_flag = '0'
        and threat_template_id = #{threatLibraryId}
        and parent_id = 0
        order by d.parent_id, d.order_num
    </select>
    <select id="selectThreatLibraryByOperationId" resultType="java.lang.Long">
        select a.threat_library_id from model_config a, co_operation_model b where  a.id = b.model_id and b.operation_id = #{operationId}
    </select>
    <select id="selectThreatTreeListByType" resultType="java.lang.String">
        select string_agg (tree_name, ',')  from threat_tree where tree_id in (select t::INTEGER from unnest(string_to_array(
                #{threatType}, ',')) as t)
    </select>
</mapper>

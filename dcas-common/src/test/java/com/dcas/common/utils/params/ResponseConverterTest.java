package com.dcas.common.utils.params;

import cn.hutool.json.JSONUtil;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ResponseConverter测试类
 */
public class ResponseConverterTest {

    @Test
    public void testConvertToFormFields_SimpleJson() {
        // 准备测试数据
        String response = "{\"code\":200,\"message\":\"success\",\"data\":{\"userId\":123,\"userName\":\"testUser\",\"email\":\"<EMAIL>\"}}";
        
        // 创建OutParam
        OutParam outParam = new OutParam();
        List<OutParam.ColumnParam> outColumns = new ArrayList<>();
        outParam.setColumnList(outColumns);
        
        // 创建saveColumns
        List<InParam.ColumnParam> saveColumns = new ArrayList<>();
        
        InParam.ColumnParam userIdColumn = new InParam.ColumnParam();
        userIdColumn.setColumnName("userId");
        userIdColumn.setColumnComment("用户ID");
        userIdColumn.setType(2); // 接口结果
        userIdColumn.setLevel(1);
        userIdColumn.setLocation("data");
        userIdColumn.setColumnType("INTEGER");
        saveColumns.add(userIdColumn);
        
        InParam.ColumnParam userNameColumn = new InParam.ColumnParam();
        userNameColumn.setColumnName("userName");
        userNameColumn.setColumnComment("用户名");
        userNameColumn.setType(2); // 接口结果
        userNameColumn.setLevel(1);
        userNameColumn.setLocation("data");
        userNameColumn.setColumnType("STRING");
        saveColumns.add(userNameColumn);
        
        // 执行测试
        List<IntegrationFormFieldVO> result = ResponseConverter.convertToFormFields(response, outParam, new ArrayList<>(), saveColumns);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        IntegrationFormFieldVO userIdField = result.stream()
                .filter(field -> "userId".equals(field.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(userIdField);
        assertEquals("123", userIdField.getValue());
        assertEquals("用户ID", userIdField.getLabel());
        assertEquals("INTEGER", userIdField.getColumnType());
        assertEquals("number", userIdField.getType());
        assertEquals(Integer.valueOf(1), userIdField.getParamType());
        assertEquals(Integer.valueOf(2), userIdField.getDataType());
        
        IntegrationFormFieldVO userNameField = result.stream()
                .filter(field -> "userName".equals(field.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(userNameField);
        assertEquals("testUser", userNameField.getValue());
        assertEquals("用户名", userNameField.getLabel());
        assertEquals("STRING", userNameField.getColumnType());
        assertEquals("text", userNameField.getType());
    }

    @Test
    public void testConvertToFormFields_NestedJson() {
        // 准备嵌套JSON测试数据
        String response = "{\"result\":{\"user\":{\"profile\":{\"name\":\"John\",\"age\":30}}}}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        List<InParam.ColumnParam> saveColumns = new ArrayList<>();
        
        InParam.ColumnParam nameColumn = new InParam.ColumnParam();
        nameColumn.setColumnName("name");
        nameColumn.setColumnComment("姓名");
        nameColumn.setType(2);
        nameColumn.setLevel(3);
        nameColumn.setLocation("result/user/profile");
        nameColumn.setColumnType("STRING");
        saveColumns.add(nameColumn);
        
        List<IntegrationFormFieldVO> result = ResponseConverter.convertToFormFields(response, outParam, new ArrayList<>(), saveColumns);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("John", result.get(0).getValue());
        assertEquals("name", result.get(0).getName());
    }

    @Test
    public void testConvertToFormFields_EmptyResponse() {
        String response = "";
        OutParam outParam = new OutParam();
        List<InParam.ColumnParam> saveColumns = new ArrayList<>();
        
        List<IntegrationFormFieldVO> result = ResponseConverter.convertToFormFields(response, outParam, new ArrayList<>(), saveColumns);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToFormFields_NullParameters() {
        List<IntegrationFormFieldVO> result = ResponseConverter.convertToFormFields(null, null, null, null);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToFormFields_ArrayResponse() {
        String response = "{\"data\":[{\"id\":1,\"name\":\"item1\"},{\"id\":2,\"name\":\"item2\"}]}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        List<InParam.ColumnParam> saveColumns = new ArrayList<>();
        
        InParam.ColumnParam idColumn = new InParam.ColumnParam();
        idColumn.setColumnName("id");
        idColumn.setColumnComment("ID");
        idColumn.setType(2);
        idColumn.setLevel(1);
        idColumn.setLocation("data");
        idColumn.setColumnType("INTEGER");
        saveColumns.add(idColumn);
        
        List<IntegrationFormFieldVO> result = ResponseConverter.convertToFormFields(response, outParam, new ArrayList<>(), saveColumns);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        // 应该返回数组中的第一个匹配值或者数组本身
        assertNotNull(result.get(0).getValue());
    }

    @Test
    public void testGetInputTypeByColumnType() {
        // 通过反射测试私有方法或者通过公共接口间接测试
        // 这里我们通过创建字段来间接测试类型转换
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        List<InParam.ColumnParam> saveColumns = new ArrayList<>();
        
        // 测试不同的字段类型
        String[] columnTypes = {"INTEGER", "BOOLEAN", "DATE", "DATETIME", "EMAIL", "PASSWORD"};
        String[] expectedTypes = {"number", "checkbox", "date", "datetime", "email", "password"};
        
        for (int i = 0; i < columnTypes.length; i++) {
            InParam.ColumnParam column = new InParam.ColumnParam();
            column.setColumnName("test" + i);
            column.setType(2);
            column.setLevel(1);
            column.setLocation("");
            column.setColumnType(columnTypes[i]);
            saveColumns.add(column);
        }
        
        String response = "{\"test0\":123,\"test1\":true,\"test2\":\"2023-01-01\",\"test3\":\"2023-01-01 12:00:00\",\"test4\":\"<EMAIL>\",\"test5\":\"password123\"}";
        
        List<IntegrationFormFieldVO> result = ResponseConverter.convertToFormFields(response, outParam, new ArrayList<>(), saveColumns);
        
        assertEquals(columnTypes.length, result.size());
        
        for (int i = 0; i < result.size(); i++) {
            assertEquals(expectedTypes[i], result.get(i).getType(), 
                "Field type mismatch for column type: " + columnTypes[i]);
        }
    }
}

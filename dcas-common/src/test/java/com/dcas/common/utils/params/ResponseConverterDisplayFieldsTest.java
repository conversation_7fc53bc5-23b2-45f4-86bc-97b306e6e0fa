package com.dcas.common.utils.params;

import cn.hutool.json.JSONUtil;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ResponseConverter显示字段过滤功能测试类
 */
public class ResponseConverterDisplayFieldsTest {

    @Test
    public void testFilterDisplayFields_SimpleObject() {
        // 测试简单对象的显示字段过滤
        String response = "{\"data\":{\"id\":1,\"name\":\"John\",\"age\":25,\"email\":\"<EMAIL>\",\"internal_id\":\"xyz\"}}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        // 配置显示字段：name, email
        List<IntegrationFormFieldVO> interfaceParams = new ArrayList<>();
        
        IntegrationFormFieldVO nameField = new IntegrationFormFieldVO();
        nameField.setName("name");
        nameField.setDisplayName("name");
        nameField.setLocation("data");
        nameField.setParamType(2); // 出参
        nameField.setDataRequired(1); // 结果展示
        interfaceParams.add(nameField);
        
        IntegrationFormFieldVO emailField = new IntegrationFormFieldVO();
        emailField.setName("email");
        emailField.setDisplayName("email");
        emailField.setLocation("data");
        emailField.setParamType(2); // 出参
        emailField.setDataRequired(1); // 结果展示
        interfaceParams.add(emailField);
        
        String result = ResponseConverter.filterDisplayFields(response, outParam, interfaceParams);
        
        assertNotNull(result);
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"email\":\"<EMAIL>\""));
        assertFalse(result.contains("\"age\":25"));
        assertFalse(result.contains("\"internal_id\":\"xyz\""));
        
        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_ArrayData() {
        // 测试数组数据的显示字段过滤
        String response = "{\"users\":[{\"id\":1,\"name\":\"John\",\"age\":25,\"email\":\"<EMAIL>\"},{\"id\":2,\"name\":\"Jane\",\"age\":30,\"email\":\"<EMAIL>\"}]}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        // 配置显示字段：只显示name
        List<IntegrationFormFieldVO> interfaceParams = new ArrayList<>();
        
        IntegrationFormFieldVO nameField = new IntegrationFormFieldVO();
        nameField.setName("name");
        nameField.setDisplayName("name");
        nameField.setLocation("users");
        nameField.setParamType(2); // 出参
        nameField.setDataRequired(1); // 结果展示
        interfaceParams.add(nameField);
        
        String result = ResponseConverter.filterDisplayFields(response, outParam, interfaceParams);
        
        assertNotNull(result);
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"name\":\"Jane\""));
        assertFalse(result.contains("\"age\""));
        assertFalse(result.contains("\"email\""));
        assertFalse(result.contains("\"id\""));
        
        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_NestedData() {
        // 测试嵌套数据的显示字段过滤
        String response = "{\"result\":{\"user\":{\"profile\":{\"name\":\"John\",\"age\":25},\"contact\":{\"email\":\"<EMAIL>\",\"phone\":\"123456789\"}}}}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        // 配置显示字段：profile中的name和contact中的email
        List<IntegrationFormFieldVO> interfaceParams = new ArrayList<>();
        
        IntegrationFormFieldVO nameField = new IntegrationFormFieldVO();
        nameField.setName("name");
        nameField.setDisplayName("name");
        nameField.setLocation("result/user/profile");
        nameField.setParamType(2); // 出参
        nameField.setDataRequired(1); // 结果展示
        interfaceParams.add(nameField);
        
        IntegrationFormFieldVO emailField = new IntegrationFormFieldVO();
        emailField.setName("email");
        emailField.setDisplayName("email");
        emailField.setLocation("result/user/contact");
        emailField.setParamType(2); // 出参
        emailField.setDataRequired(1); // 结果展示
        interfaceParams.add(emailField);
        
        String result = ResponseConverter.filterDisplayFields(response, outParam, interfaceParams);
        
        assertNotNull(result);
        assertTrue(result.contains("\"name\":\"John\""));
        assertTrue(result.contains("\"email\":\"<EMAIL>\""));
        assertFalse(result.contains("\"age\":25"));
        assertFalse(result.contains("\"phone\":\"123456789\""));
        
        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_NoDisplayFields() {
        // 测试没有配置显示字段的情况
        String response = "{\"data\":{\"id\":1,\"name\":\"John\"}}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        List<IntegrationFormFieldVO> interfaceParams = new ArrayList<>();
        
        String result = ResponseConverter.filterDisplayFields(response, outParam, interfaceParams);
        
        assertEquals(response, result); // 应该返回原始数据
    }

    @Test
    public void testFilterDisplayFields_EmptyResponse() {
        // 测试空响应的情况
        String response = "";
        
        OutParam outParam = new OutParam();
        List<IntegrationFormFieldVO> interfaceParams = new ArrayList<>();
        
        String result = ResponseConverter.filterDisplayFields(response, outParam, interfaceParams);
        
        assertEquals("", result);
    }

    @Test
    public void testFilterDisplayFields_FallbackToName() {
        // 测试displayName为空时回退到name字段
        String response = "{\"data\":{\"id\":1,\"name\":\"John\",\"age\":25}}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        List<IntegrationFormFieldVO> interfaceParams = new ArrayList<>();
        
        IntegrationFormFieldVO nameField = new IntegrationFormFieldVO();
        nameField.setName("name");
        nameField.setDisplayName(""); // 空的displayName
        nameField.setLocation("data");
        nameField.setParamType(2); // 出参
        nameField.setDataRequired(1); // 结果展示
        interfaceParams.add(nameField);
        
        String result = ResponseConverter.filterDisplayFields(response, outParam, interfaceParams);
        
        assertNotNull(result);
        assertTrue(result.contains("\"name\":\"John\""));
        assertFalse(result.contains("\"age\":25"));
        assertFalse(result.contains("\"id\":1"));
        
        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }

    @Test
    public void testFilterDisplayFields_MixedParamTypes() {
        // 测试混合参数类型，只有paramType=2且dataRequired=1的字段应该被包含
        String response = "{\"data\":{\"id\":1,\"name\":\"John\",\"age\":25,\"email\":\"<EMAIL>\"}}";
        
        OutParam outParam = new OutParam();
        outParam.setColumnList(new ArrayList<>());
        
        List<IntegrationFormFieldVO> interfaceParams = new ArrayList<>();
        
        // 显示字段
        IntegrationFormFieldVO nameField = new IntegrationFormFieldVO();
        nameField.setName("name");
        nameField.setDisplayName("name");
        nameField.setLocation("data");
        nameField.setParamType(2); // 出参
        nameField.setDataRequired(1); // 结果展示
        interfaceParams.add(nameField);
        
        // 非显示字段（paramType=1）
        IntegrationFormFieldVO ageField = new IntegrationFormFieldVO();
        ageField.setName("age");
        ageField.setDisplayName("age");
        ageField.setLocation("data");
        ageField.setParamType(1); // 入参
        ageField.setDataRequired(1); // 结果展示
        interfaceParams.add(ageField);
        
        // 非显示字段（dataRequired=2）
        IntegrationFormFieldVO emailField = new IntegrationFormFieldVO();
        emailField.setName("email");
        emailField.setDisplayName("email");
        emailField.setLocation("data");
        emailField.setParamType(2); // 出参
        emailField.setDataRequired(2); // 筛选条件
        interfaceParams.add(emailField);
        
        String result = ResponseConverter.filterDisplayFields(response, outParam, interfaceParams);
        
        assertNotNull(result);
        assertTrue(result.contains("\"name\":\"John\""));
        assertFalse(result.contains("\"age\":25"));
        assertFalse(result.contains("\"email\":\"<EMAIL>\""));
        assertFalse(result.contains("\"id\":1"));
        
        System.out.println("Original: " + response);
        System.out.println("Filtered: " + result);
    }
}

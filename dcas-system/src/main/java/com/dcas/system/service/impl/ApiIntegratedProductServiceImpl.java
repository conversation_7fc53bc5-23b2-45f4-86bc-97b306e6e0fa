package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.domain.entity.ApiIntegratedProduct;
import com.dcas.common.enums.IntegrationStatusEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.ApiIntegratedProductMapper;
import com.dcas.common.mapper.ApiInterfaceMapper;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.ProductInfoVO;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.common.utils.Func;
import com.dcas.system.service.ApiIntegratedProductService;
import com.dcas.system.service.IApiCallService;
import com.dcas.system.service.IApiReleaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 集成产品服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiIntegratedProductServiceImpl extends ServiceImpl<ApiIntegratedProductMapper, ApiIntegratedProduct> implements ApiIntegratedProductService {

    private final IApiCallService apiCallService;
    private final IApiReleaseService apiReleaseService;
    private final ApiInterfaceMapper apiInterfaceMapper;

    @Override
    public List<ProductIntegrationVO> getIntegrationPage() {
        List<ApiIntegratedProduct> apiIntegratedProducts = baseMapper.selectList(new QueryWrapper<>());
        return apiIntegratedProducts.stream().collect(Collectors.groupingBy(ApiIntegratedProduct::getCapability)).entrySet().stream().map(e -> {
            ProductIntegrationVO vo = new ProductIntegrationVO();
            vo.setCapability(e.getKey());
            vo.setProducts(e.getValue().stream().map(apiIntegratedProduct -> {
                ProductInfoVO productInfoVO = new ProductInfoVO();
                productInfoVO.setId(apiIntegratedProduct.getId());
                productInfoVO.setReleaseId(apiIntegratedProduct.getReleaseId());
                productInfoVO.setCapability(apiIntegratedProduct.getCapability());
                productInfoVO.setName(apiIntegratedProduct.getName());
                productInfoVO.setUrl(apiIntegratedProduct.getUrl());
                productInfoVO.setProductName(apiIntegratedProduct.getProductName());
                productInfoVO.setCompany(apiIntegratedProduct.getCompany());
                productInfoVO.setLogo(apiIntegratedProduct.getLogo());
                productInfoVO.setStatus(apiIntegratedProduct.getStatus());
                productInfoVO.setCreateTime(apiIntegratedProduct.getCreateTime());
                productInfoVO.setCreateBy(apiIntegratedProduct.getCreateBy());
                productInfoVO.setUpdateTime(apiIntegratedProduct.getUpdateTime());
                productInfoVO.setUpdateBy(apiIntegratedProduct.getUpdateBy());
                //productInfoVO.setParams(JSONUtil.parseObj(apiIntegratedProduct.getConfigParams()).toBean(new TypeReference<List<IntegrationFormFieldVO>>(){}));
                return productInfoVO;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createIntegration(CreateIntegrationReq request) {
        // 检查集成名称是否重复
        LambdaQueryWrapper<ApiIntegratedProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiIntegratedProduct::getCapability, request.getCapability());
        wrapper.eq(ApiIntegratedProduct::getProductName, request.getProductName());
        ApiIntegratedProduct existing = this.getOne(wrapper);
        if (existing != null) {
            throw new RuntimeException("当前产品已对接");
        }

        // 创建集成产品记录
        ApiIntegratedProduct apiIntegratedProduct = new ApiIntegratedProduct();
        apiIntegratedProduct.setReleaseId(request.getReleaseId());
        apiIntegratedProduct.setCapability(request.getCapability());
        apiIntegratedProduct.setName(request.getName());
        apiIntegratedProduct.setUrl(request.getUrl());
        apiIntegratedProduct.setProductName(request.getProductName());
        apiIntegratedProduct.setCompany(request.getCompany());
        apiIntegratedProduct.setLogo(request.getLogo());
        apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
        apiIntegratedProduct.setVersion(request.getVersion());
        Func.beforeInsert(apiIntegratedProduct);

        // 转换配置参数为JSON字符串
        if (StrUtil.isNotEmpty(apiIntegratedProduct.getConfigParams())) {
            apiIntegratedProduct.setConfigParams(JSONUtil.toJsonStr(request.getConfigParams()));
        }

        // 测试连接
        List<ApiInterface> apiInterfaces = apiInterfaceMapper.selectByReleaseId(apiIntegratedProduct.getReleaseId());
        if (CollUtil.isNotEmpty(apiInterfaces)) {
            // 根据排序选择第一个接口
            ApiInterface apiInterface = apiInterfaces.get(0);
            // 使用鉴权参数验证连接
            if (apiCallService.testApiConnection(apiInterface, request.getConfigParams(), apiIntegratedProduct.getUrl())) {
                apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
            } else {
                throw new ServiceException("连接测试失败");
            }
        } else {
            throw new ServiceException("未找到接口信息");
        }

        this.save(apiIntegratedProduct);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIntegration(UpdateIntegrationReq request) {
        ApiIntegratedProduct apiIntegratedProduct = this.getById(request.getId());
        if (apiIntegratedProduct == null) {
            throw new RuntimeException("集成产品不存在");
        }

        apiIntegratedProduct.setReleaseId(request.getReleaseId());
        apiIntegratedProduct.setCapability(request.getCapability());
        apiIntegratedProduct.setName(request.getName());
        apiIntegratedProduct.setUrl(request.getUrl());
        apiIntegratedProduct.setProductName(request.getProductName());
        apiIntegratedProduct.setCompany(request.getCompany());
        apiIntegratedProduct.setLogo(request.getLogo());
        apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
        apiIntegratedProduct.setVersion(request.getVersion());

        // 更新集成产品信息
        Func.beforeUpdate(apiIntegratedProduct);

        // 转换配置参数为JSON字符串
        if (StrUtil.isNotEmpty(apiIntegratedProduct.getConfigParams())) {
            apiIntegratedProduct.setConfigParams(JSONUtil.toJsonStr(request.getConfigParams()));
        }

        // 测试连接
        List<ApiInterface> apiInterfaces = apiInterfaceMapper.selectByReleaseId(apiIntegratedProduct.getReleaseId());
        if (CollUtil.isNotEmpty(apiInterfaces)) {
            // 根据排序选择第一个接口
            ApiInterface apiInterface = apiInterfaces.get(0);
            if (apiCallService.testApiConnection(apiInterface, request.getConfigParams(), apiIntegratedProduct.getUrl())) {
                apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
            } else {
                throw new ServiceException("连接测试失败");
            }
        } else {
            throw new ServiceException("未找到接口信息");
        }

        updateById(apiIntegratedProduct);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIntegrations(IdsReq ids) {
        removeByIds(ids.getIds());
    }

    @Override
    public ProductInfoVO getProductInfo(Long id) {
        ApiIntegratedProduct apiIntegratedProduct = this.getById(id);
        if (apiIntegratedProduct == null) {
            throw new ServiceException("对接产品不存在");
        }

        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVO.setId(apiIntegratedProduct.getId());
        productInfoVO.setReleaseId(apiIntegratedProduct.getReleaseId());
        productInfoVO.setCapability(apiIntegratedProduct.getCapability());
        productInfoVO.setName(apiIntegratedProduct.getName());
        productInfoVO.setUrl(apiIntegratedProduct.getUrl());
        productInfoVO.setProductName(apiIntegratedProduct.getProductName());
        productInfoVO.setCompany(apiIntegratedProduct.getCompany());
        productInfoVO.setLogo(apiIntegratedProduct.getLogo());
        productInfoVO.setStatus(apiIntegratedProduct.getStatus());
        productInfoVO.setCreateTime(apiIntegratedProduct.getCreateTime());
        productInfoVO.setCreateBy(apiIntegratedProduct.getCreateBy());
        productInfoVO.setUpdateTime(apiIntegratedProduct.getUpdateTime());
        productInfoVO.setUpdateBy(apiIntegratedProduct.getUpdateBy());

        // 状态为参数更新需要获取新的参数展示
        if (StrUtil.equals(apiIntegratedProduct.getStatus(), IntegrationStatusEnum.PARAMETER_UPDATED.getCode())) {
            List<IntegrationFormFieldVO> params = apiReleaseService.getProductHeader(apiIntegratedProduct.getReleaseId());
            productInfoVO.setParams(params);
        } else {
            productInfoVO.setParams(JSONUtil.parseObj(apiIntegratedProduct.getConfigParams()).toBean(new TypeReference<List<IntegrationFormFieldVO>>(){}));
        }
        return productInfoVO;
    }
}

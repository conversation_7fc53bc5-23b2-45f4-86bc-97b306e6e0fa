package com.dcas.system.service;

import com.dcas.common.domain.entity.WorkflowTask;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.WorkflowTaskReq;
import com.dcas.common.model.req.WorkflowTaskUpdateReq;
import com.dcas.common.utils.PageResult;

import java.util.concurrent.CompletableFuture;

/**
 * 工作流任务Service接口
 * 
 * <AUTHOR>
 */
public interface IWorkflowTaskService {
    
    /**
     * 查询工作流任务
     *
     * @param taskId 工作流任务主键
     * @return 工作流任务
     */
    WorkflowTask selectWorkflowTaskByTaskId(Long taskId);

    /**
     * 查询工作流任务列表
     *
     * @param req 工作流任务
     * @return 工作流任务集合
     */
    PageResult<WorkflowTask> selectWorkflowTaskList(WorkflowTaskReq req);

    /**
     * 新增工作流任务
     *
     * @param workflowTask 工作流任务
     */
    void insertWorkflowTask(WorkflowTask workflowTask);

    /**
     * 修改工作流任务
     *
     * @param req 工作流任务
     */
    void updateWorkflowTask(WorkflowTaskUpdateReq req);

    /**
     * 批量删除工作流任务
     *
     * @param req 需要删除的工作流任务主键集合
     */
    void deleteWorkflowTaskByTaskIds(IdsReq req);

    /**
     * 删除工作流任务信息
     *
     * @param taskId 工作流任务主键
     * @return 结果
     */
    int deleteWorkflowTaskByTaskId(Long taskId);

    /**
     * 异步执行工作流任务
     *
     * @param taskId 任务ID
     * @return CompletableFuture
     */
    CompletableFuture<Void> executeTaskAsync(Long taskId);

    /**
     * 同步执行工作流任务
     *
     * @param taskId 任务ID
     */
    void executeTask(Long taskId);

    /**
     * 终止正在执行的任务
     *
     * @param taskId 任务ID
     * @param reason 终止原因
     * @return 结果
     */
    boolean terminateTask(Long taskId, String reason);

    /**
     * 重启失败或终止的任务
     *
     * @param taskId 任务ID
     */
    void restartTask(Long taskId);

    /**
     * 检查任务是否可以执行
     *
     * @param taskId 任务ID
     * @return 是否可以执行
     */
    boolean canExecuteTask(Long taskId);

    /**
     * 检查任务是否可以终止
     *
     * @param taskId 任务ID
     * @return 是否可以终止
     */
    boolean canTerminateTask(Long taskId);

    /**
     * 检查是否需要清理任务上下文
     *
     * @param taskId 任务ID
     */
    boolean needClearTaskContext(Long taskId);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 结果
     */
    boolean updateTaskStatus(Long taskId, Integer status);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param currentStep 当前步骤
     * @param progressPercentage 进度百分比
     * @return 结果
     */
    boolean updateTaskProgress(Long taskId, Integer currentStep, Integer progressPercentage);

    /**
     * 记录任务错误
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 结果
     */
    boolean recordTaskError(Long taskId, String errorMessage);
}

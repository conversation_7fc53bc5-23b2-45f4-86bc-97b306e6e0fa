package com.dcas.system.service.impl;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.TaskStep;
import com.dcas.common.enums.JobStatusEnum;
import com.dcas.common.enums.StepStatusEnum;
import com.dcas.common.mapper.TaskStepMapper;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.IntegrationInterfaceVO;
import com.dcas.common.utils.DateUtils;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.params.ResponseConverter;
import com.dcas.system.service.ITaskStepService;
import com.dcas.system.service.IApiCallService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 任务步骤Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskStepServiceImpl extends ServiceImpl<TaskStepMapper, TaskStep> implements ITaskStepService {

    private final IApiCallService apiCallService;

    /**
     * 查询任务步骤
     * 
     * @param stepId 任务步骤主键
     * @return 任务步骤
     */
    @Override
    public TaskStep selectTaskStepByStepId(Long stepId) {
        return baseMapper.selectTaskStepByStepId(stepId);
    }

    /**
     * 查询任务步骤列表
     * 
     * @param taskStep 任务步骤
     * @return 任务步骤
     */
    @Override
    public List<TaskStep> selectTaskStepList(TaskStep taskStep) {
        return baseMapper.selectTaskStepList(taskStep);
    }

    /**
     * 根据任务ID查询步骤列表
     * 
     * @param taskId 任务ID
     * @return 步骤列表
     */
    @Override
    public List<TaskStep> selectTaskStepByTaskId(Long taskId) {
        return baseMapper.selectTaskStepByTaskId(taskId);
    }

    /**
     * 新增任务步骤
     * 
     * @param taskStep 任务步骤
     * @return 结果
     */
    @Override
    public int insertTaskStep(TaskStep taskStep) {
        taskStep.setCreateTime(DateUtils.getNowDate());
        taskStep.setCreateBy(getUsername());
        taskStep.setStatus(JobStatusEnum.UN_START.getCode());
        taskStep.setRetryCount(0);
        
        if (taskStep.getMaxRetries() == null) {
            taskStep.setMaxRetries(3);
        }
        if (taskStep.getTimeoutSeconds() == null) {
            taskStep.setTimeoutSeconds(30);
        }
        
        return baseMapper.insertTaskStep(taskStep);
    }

    /**
     * 修改任务步骤
     * 
     * @param taskStep 任务步骤
     * @return 结果
     */
    @Override
    public int updateTaskStep(TaskStep taskStep) {
        return baseMapper.updateTaskStep(taskStep);
    }

    /**
     * 批量删除任务步骤
     * 
     * @param stepIds 需要删除的任务步骤主键
     * @return 结果
     */
    @Override
    public int deleteTaskStepByStepIds(Long[] stepIds) {
        return baseMapper.deleteTaskStepByStepIds(stepIds);
    }

    /**
     * 删除任务步骤信息
     * 
     * @param stepId 任务步骤主键
     * @return 结果
     */
    @Override
    public int deleteTaskStepByStepId(Long stepId) {
        return baseMapper.deleteTaskStepByStepId(stepId);
    }

    /**
     * 根据任务ID删除所有步骤
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteTaskStepByTaskId(Long taskId) {
        return baseMapper.deleteTaskStepByTaskId(taskId);
    }

    /**
     * 执行单个步骤
     * 
     * @param step 步骤
     * @param previousStepOutput 前一步骤的输出数据
     * @return 执行结果
     */
    @Override
    public boolean executeStep(TaskStep step, TaskStep nextStep, String previousStepOutput, IntegrationInterfaceVO interfaceParam, String url) {
        log.info("开始执行步骤，步骤ID: {}", step.getId());

        Long stepId = step.getId();
        Date startTime = DateUtils.getNowDate();
        
        try {
            // 更新步骤状态为执行中
            updateStepStatus(stepId, StepStatusEnum.RUNNING.getCode());

            List<IntegrationFormFieldVO> params = new ArrayList<>();
            if (Objects.nonNull(interfaceParam)) {
                params = interfaceParam.getConfigParams().stream().filter(p -> Objects.equals(p.getParamType(), 1)).collect(Collectors.toList());
                // 上一个步骤的输出需要这个步骤作为接口的输入的数据
                List<IntegrationFormFieldVO> previousResult = JSONUtil.parseObj(previousStepOutput).toBean(new TypeReference<List<IntegrationFormFieldVO>>() {
                });
                params.addAll(previousResult);
            }
            
            // 执行API调用
            IApiCallService.ApiCallResult result = apiCallService.executeApiCall(step, params, url);
            
            Date endTime = DateUtils.getNowDate();
            long duration = endTime.getTime() - startTime.getTime();
            
            // 记录执行时间
            recordStepExecutionTime(stepId, startTime, endTime, duration);
            
            if (result.isSuccess()) {
                // 如果下一步不为空，则中间步骤不存储全量内容，根据筛选字段筛选然后只保存下一个接口调用所需要用到的内容
                if (Objects.nonNull(nextStep)) {
                    OutParam outParam = JSONUtil.toBean(step.getResponseFormat(), OutParam.class);
                    // 当前接口配置的出参中，参数要求为筛选条件的参数，用于筛选一部分需要的响应内容
                    List<IntegrationFormFieldVO> filterParam = interfaceParam.getConfigParams().stream().filter(p ->
                            Objects.equals(p.getParamType(), 2)).collect(Collectors.toList());
                    InParam nextInParams = JSONUtil.toBean(nextStep.getRequestParams(), InParam.class);
                    // 下个接口请求入参中上个接口的参数来源，中间接口只保留下个接口需要用到的结果
                    List<InParam.ColumnParam> saveParams = nextInParams.getColumnList().stream().filter(p ->
                            Objects.equals(p.getType(), 2)).collect(Collectors.toList());
                    List<IntegrationFormFieldVO> convertedFields = ResponseConverter.convertToFormFields(result.getResponseData(), outParam, filterParam, saveParams);
                    // 将转换后的字段数据作为输出数据存储，供下一个接口使用
                    if (!convertedFields.isEmpty()) {
                        String outputData = JSONUtil.toJsonStr(convertedFields);
                        recordStepOutput(stepId, outputData);
                        log.info("步骤 {} 转换并保存了 {} 个字段用于下一接口", stepId, convertedFields.size());
                    } else {
                        // 如果没有转换字段，使用原始输出数据
                        recordStepOutput(stepId, result.getOutputData());
                    }
                } else {
                    // 如果没有下一步，直接使用原始输出数据
                    recordStepOutput(stepId, result.getOutputData());
                }
                // 记录成功结果
                recordStepResponse(stepId, result.getStatusCode(), result.getResponseData());
                updateStepStatus(stepId, StepStatusEnum.COMPLETED.getCode());
                
                log.info("步骤执行成功，步骤ID: {}, 执行时间: {}ms", stepId, duration);
                return true;
            } else {
                // 记录失败结果
                recordStepResponse(stepId, result.getStatusCode(), result.getResponseData());
                recordStepError(stepId, result.getErrorMessage());
                
                // 检查是否需要重试
                if (canRetryStep(stepId)) {
                    log.info("步骤执行失败，准备重试，步骤ID: {}", stepId);
                    incrementStepRetryCount(stepId);
                    return executeStep(step, nextStep, previousStepOutput, interfaceParam, url); // 递归重试
                } else {
                    updateStepStatus(stepId, StepStatusEnum.FAILED.getCode());
                    log.error("步骤执行失败，步骤ID: {}, 错误: {}", stepId, result.getErrorMessage());
                    return false;
                }
            }
            
        } catch (Exception e) {
            Date endTime = DateUtils.getNowDate();
            long duration = endTime.getTime() - startTime.getTime();
            
            recordStepExecutionTime(stepId, startTime, endTime, duration);
            recordStepError(stepId, "步骤执行异常: " + e.getMessage());
            updateStepStatus(stepId, StepStatusEnum.FAILED.getCode());
            
            log.error("步骤执行异常，步骤ID: {}", stepId, e);
            return false;
        }
    }

    /**
     * 获取任务的下一个待执行步骤
     * 
     * @param taskId 任务ID
     * @return 下一个步骤
     */
    @Override
    public TaskStep getNextPendingStep(Long taskId) {
        return baseMapper.selectNextPendingStep(taskId);
    }

    /**
     * 根据任务ID和步骤顺序获取步骤
     * 
     * @param taskId 任务ID
     * @param stepOrder 步骤顺序
     * @return 任务步骤
     */
    @Override
    public TaskStep getTaskStepByOrder(Long taskId, Integer stepOrder) {
        return baseMapper.selectTaskStepByTaskIdAndOrder(taskId, stepOrder);
    }

    /**
     * 更新步骤状态
     * 
     * @param stepId 步骤ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    public boolean updateStepStatus(Long stepId, String status) {
        return baseMapper.updateTaskStepStatus(stepId, status, getUsername()) > 0;
    }

    /**
     * 记录步骤执行时间
     * 
     * @param stepId 步骤ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param executionDuration 执行时长
     * @return 结果
     */
    @Override
    public boolean recordStepExecutionTime(Long stepId, Date startTime, Date endTime, Long executionDuration) {
        return baseMapper.updateTaskStepExecutionTime(stepId, startTime, endTime, executionDuration, getUsername()) > 0;
    }

    /**
     * 记录步骤响应信息
     * 
     * @param stepId 步骤ID
     * @param responseStatus 响应状态码
     * @param responseData 响应数据
     * @return 结果
     */
    @Override
    public boolean recordStepResponse(Long stepId, Integer responseStatus, String responseData) {
        return baseMapper.updateTaskStepResponse(stepId, responseStatus, responseData, getUsername()) > 0;
    }

    /**
     * 记录步骤错误信息
     * 
     * @param stepId 步骤ID
     * @param errorMessage 错误信息
     * @return 结果
     */
    @Override
    public boolean recordStepError(Long stepId, String errorMessage) {
        return baseMapper.updateTaskStepError(stepId, errorMessage, getUsername()) > 0;
    }

    /**
     * 记录步骤输出数据
     * 
     * @param stepId 步骤ID
     * @param outputData 输出数据
     * @return 结果
     */
    @Override
    public boolean recordStepOutput(Long stepId, String outputData) {
        return baseMapper.updateTaskStepOutput(stepId, outputData, getUsername()) > 0;
    }

    /**
     * 增加步骤重试次数
     * 
     * @param stepId 步骤ID
     * @return 结果
     */
    @Override
    public boolean incrementStepRetryCount(Long stepId) {
        return baseMapper.incrementStepRetryCount(stepId, getUsername()) > 0;
    }

    /**
     * 统计任务中指定状态的步骤数量
     * 
     * @param taskId 任务ID
     * @param status 状态
     * @return 步骤数量
     */
    @Override
    public int countTaskStepsByStatus(Long taskId, String status) {
        return baseMapper.countTaskStepsByTaskIdAndStatus(taskId, status);
    }

    /**
     * 检查步骤是否可以重试
     *
     * @param stepId 步骤ID
     * @return 是否可以重试
     */
    @Override
    public boolean canRetryStep(Long stepId) {
        return false;
    }

    /**
     * 重置步骤状态为待执行
     *
     * @param stepId 步骤ID
     * @return 结果
     */
    @Override
    public boolean resetStepToPending(Long stepId) {
        TaskStep step = selectTaskStepByStepId(stepId);
        if (step == null) {
            return false;
        }

        // 重置步骤状态和相关字段
        step.setStatus(JobStatusEnum.UN_START.getCode());
        step.setRetryCount(0);
        step.setStartTime(null);
        step.setEndTime(null);
        step.setExecutionDuration(null);
        step.setResponseStatus(null);
        step.setResponseData(null);
        step.setErrorMessage(null);
        step.setOutputData(null);

        return updateTaskStep(step) > 0;
    }

    /**
     * 获取当前用户名
     */
    private String getUsername() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            return "system";
        }
    }
}

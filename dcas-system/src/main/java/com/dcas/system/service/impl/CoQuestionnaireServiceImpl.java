package com.dcas.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.exception.params.FailParamsException;
import com.github.pagehelper.PageHelper;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.utils.law.LawCodeUtil;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.dto.AddVerificationDTO;
import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.model.dto.SaveQuestionnaireDTO;
import com.dcas.common.model.dto.SaveQuestionnaireInsider;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.Article;
import com.dcas.common.model.vo.QueryQuestionnaireInsider;
import com.dcas.common.model.vo.QueryQuestionnaireVo;
import com.dcas.common.mapper.*;
import com.dcas.system.service.CoQuestionnaireService;
import com.dcas.system.service.CoVerificationService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 问卷调研服务层
 *
 * <AUTHOR>
 * @Date 2022/6/21 15:14
 * @ClassName QuestionnaireServiceImpl
 */
@Service
public class CoQuestionnaireServiceImpl extends ServiceImpl<CoQuestionnaireMapper, CoQuestionnaire> implements CoQuestionnaireService {

    @Autowired
    private CoQuestionnaireMapper coQuestionnaireMapper;
    @Autowired
    private CoFileMapper coFileMapper;
    @Autowired
    private CoQuestionMapper coQuestionMapper;
    @Autowired
    private CoLawDocMapper coLawDocMapper;
    @Autowired
    private CoSurveyObjectTypeMapper coSurveyObjectTypeMapper;
    @Autowired
    private CoVerificationService coVerificationService;


    /**
     * 保存更新问卷
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/5/31 10:33
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int saveQuestionnaire(RequestModel<SaveQuestionnaireDTO> dto) throws Exception {
        CheckUtil.checkParams(dto.getPrivator());
        //查询问题表中按labelId分组的问题数量; 106--185;107--72;108---41;109--
        PageHelper.clearPage();
        List<CoQuestion> coQuestions = coQuestionMapper.queryQuestionByLabelId(dto.getPrivator().getLabelId());
        List<Integer> questionIds = coQuestions.stream().map(p -> p.getQuestionId()).collect(Collectors.toList());
        List<Integer> questionIdList = dto.getPrivator().getInsiderList().stream().map(p -> p.getQuestionId()).collect(Collectors.toList());
        //入参校验:入参问题id必须存在于问题表列表中; true则B是A的子集
        if (!questionIds.containsAll(questionIdList)) {
            throw new FailParamsException(LabelEnum.getNameByCode(dto.getPrivator().getLabelId()) + "不包含相关问题");
        }
        //入参校验: 校验子树问题是否重复
        Set set = new HashSet<>(questionIdList);
        if (set.size() != questionIdList.size()) {
            throw new FailParamsException(LabelEnum.getNameByCode(dto.getPrivator().getLabelId()) + "问题Id存在重复");
        }

        //更新前先查询： 在问卷调研表已存在记录就去更新，否则新增
        QueryWrapper<CoQuestionnaire> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", dto.getPrivator().getLabelId());
        //已存表问题
        List<CoQuestionnaire> collect = coQuestionnaireMapper.selectList(queryWrapper);
        List<Integer> savedIdList = collect.stream().map(CoQuestionnaire::getQuestionId).collect(Collectors.toList());

        //未做的新问题=入参问题-已做的问题；新题目执行insert、已做的问题执行update
        questionIdList.removeAll(savedIdList);
        //入参问卷列表
        List<SaveQuestionnaireInsider> insiderList = dto.getPrivator().getInsiderList();

        List<CoQuestionnaire> unInsertList = new ArrayList<>();
        List<CoQuestionnaire> unUpdateList = new ArrayList<>();
        List<Integer> updateQuestionIdList = new ArrayList<>();
        int row = 0;
        for (int i = 0; i < insiderList.size(); i++) {
            //未存表的问题执行insert
            if (questionIdList.contains(insiderList.get(i).getQuestionId())) {
                CoQuestionnaire questionnaire = new CoQuestionnaire();
                questionnaire.setOperationId(dto.getPrivator().getOperationId());
                questionnaire.setLabelId(dto.getPrivator().getLabelId());
                BeanUtils.copyProperties(dto.getPrivator().getInsiderList().get(i), questionnaire);
                String fileIds = StringUtils.join(dto.getPrivator().getInsiderList().get(i).getFileIdList().toArray(), ",");
                questionnaire.setFileIds(fileIds);
                questionnaire.setQuestionnaireId(SnowFlake.getId());
                //选项和符合性说明都不为空表示这道题已完成，用来计算进度
                if (StringUtils.isNotBlank(questionnaire.getOpt()) && StringUtils.isNotBlank(questionnaire.getRemark())) {
                    questionnaire.setCompleted(Boolean.TRUE);
                }
                CoQuestion coQuestion = coQuestionMapper.selectById(questionnaire.getQuestionId());
                questionnaire.setBpCodes(coQuestion.getBpCodes());
                questionnaire.setRemark(insiderList.get(i).getRemark());
                coQuestionnaireMapper.insert(questionnaire);
                //未存库问题
                unInsertList.add(questionnaire);
            } else {
                List<String> fileIdList = dto.getPrivator().getInsiderList().get(i).getFileIdList();
                String fileIds = StringUtils.join(fileIdList.toArray(), ",");
                UpdateWrapper<CoQuestionnaire> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("opt", dto.getPrivator().getInsiderList().get(i).getOpt());
                updateWrapper.set("remark", dto.getPrivator().getInsiderList().get(i).getRemark());
                updateWrapper.set("file_ids", fileIds);
                //要有labelId条件。因为不同labelId可能出现问题id相同的情况
                Integer questionId = dto.getPrivator().getInsiderList().get(i).getQuestionId();
                updateWrapper.eq("question_id", questionId);
                updateWrapper.eq("operation_id", dto.getPrivator().getOperationId());
                updateWrapper.eq("label_id", dto.getPrivator().getLabelId());

                //是否已完成
                if (StringUtils.isNotBlank(insiderList.get(i).getOpt()) && StringUtils.isNotBlank(insiderList.get(i).getRemark())) {
                    updateWrapper.set("completed", Boolean.TRUE);
                }
                coQuestionnaireMapper.update(new CoQuestionnaire(), updateWrapper);

                //更新的问题列表:注意不同分类有几率出现重复问题,即相同问题id的选项不同
                CoQuestionnaire questionnaire = new CoQuestionnaire();
                questionnaire.setQuestionId(questionId);
                questionnaire.setOpt(dto.getPrivator().getInsiderList().get(i).getOpt());
                CoQuestion coQuestion = coQuestionMapper.selectById(questionId);
                questionnaire.setBpCodes(coQuestion.getBpCodes());
                questionnaire.setRemark(dto.getPrivator().getInsiderList().get(i).getRemark());
                unUpdateList.add(questionnaire);
                updateQuestionIdList.add(questionId);
            }
            row++;
        }

        //更新现状核验更新的问题是选项更新而bpCode不会更新
        //已存表问题 - 更新问题
        List<CoQuestionnaire> savedList = collect.stream().filter(p -> !updateQuestionIdList.contains(p.getQuestionId())).collect(Collectors.toList());
        //(已存表问题-更新问题)+更新问题内容+待存库问题内容
        List<CoQuestionnaire> coQuestionnaires = new ArrayList<>();
        coQuestionnaires.addAll(unInsertList);
        coQuestionnaires.addAll(unUpdateList);
        coQuestionnaires.addAll(savedList);
        AddVerificationDTO addVerificationDTO = new AddVerificationDTO();
        addVerificationDTO.setOperationId(dto.getPrivator().getOperationId());
        addVerificationDTO.setLabelId(LabelEnum.NLFX.getCode());
        List<String> levelList = new ArrayList<>();
        levelList.add("3");
        addVerificationDTO.setLevelList(levelList);
        try {
            coVerificationService.saveVerification(addVerificationDTO, coQuestionnaires);
        }catch (Exception e){
            e.printStackTrace();
        }

        return row;
    }

    /**
     * 查询问卷
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/5/31 10:33
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public QueryQuestionnaireVo queryQuestionnaire(RequestModel<CommonDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());

        QueryWrapper<CoQuestionnaire> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", dto.getPrivator().getLabelId());
        List<CoQuestionnaire> coQuestionnaireList = coQuestionnaireMapper.selectList(queryWrapper);
        List<Integer> questionIdList = coQuestionnaireList.stream().map(CoQuestionnaire::getQuestionId).collect(Collectors.toList());
        //问卷表关联问题表查询
        List<QueryQuestionnaireInsider> insiderList = coQuestionnaireMapper.queryQuestionnaireList(dto.getPrivator());

        queryWrapper.eq("completed", true);
        List<CoQuestionnaire> list = coQuestionnaireMapper.selectList(queryWrapper);
        //已完成题目数量
        QueryQuestionnaireVo vo = new QueryQuestionnaireVo();
        //基本信息
        QueryWrapper<CoSurveyObjectType> query = new QueryWrapper<>();
        query.eq("label_id", dto.getPrivator().getLabelId());
        CoSurveyObjectType coSurveyObjectType = coSurveyObjectTypeMapper.selectOne(query);
        vo.setComment(coSurveyObjectType.getSurveyObjectComment());
        vo.setCompleteNum(list.size());
        //题目总数
        vo.setAllNum(insiderList.size());
        //进度,取整
        DecimalFormat df = new DecimalFormat("#0");
        String format = df.format((Double.valueOf(list.size()) / Double.valueOf(insiderList.size())) * 100);
        vo.setProgress(new BigDecimal(format));

        for (QueryQuestionnaireInsider insider : insiderList) {

            if (questionIdList.contains(insider.getQuestionId())) {
                for (CoQuestionnaire coQuestionnaire : coQuestionnaireList) {
                    //2.查询题目对应附件,只有已做完的题目才需要去查询附件
                    List<CoFile> fileList = coFileMapper.queryFile(dto.getPrivator().getOperationId(), dto.getPrivator().getLabelId(), coQuestionnaire.getQuestionId());
                    insider.setFileList(fileList);
                }
            }
            //3.查询题目对应法条
            List<Article> articleList = this.queryArticle(dto.getPrivator().getLabelId(), insider.getQuestionId());
            insider.setArticleList(articleList);
        }
        vo.setInsiderList(insiderList);
        return vo;
    }


    /**
     * 查询单条题目对应法律条文
     *
     * @param labelId
     * @return
     */
    public List<Article> queryArticle(Long labelId, Integer questionId) {
        //查询题库
        QueryQuestionnaireInsider insider = coQuestionMapper.queryQuestion(labelId, questionId);
        //法律条文id还原成list
        if (ObjectUtils.isNotEmpty(insider)) {
            if (StringUtils.isNotEmpty(insider.getArticleCodes())) {
                List<String> articleCodeList = Arrays.asList(insider.getArticleCodes().split(","));
                return this.queryArticleList(articleCodeList);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 查询单条题目对应法律条文
     *
     * @param articleCodeList request
     * @return * @return List<Article>
     * @Date 2022/6/15 10:08
     */
    public List<Article> queryArticleList(List<String> articleCodeList) {

        List<Article> articleList = new ArrayList<>();
        for (String articleCode : articleCodeList) {
            //组装法律条文解释说明
            Map<String, String> lawPoint = LawCodeUtil.getLawPoint(articleCode);

            //组装point
            QueryWrapper<CoLawDoc> query = new QueryWrapper<>();
            //查询对应法律文件
            query.eq("doc_sort", Integer.parseInt(lawPoint.get("docSort")));
            CoLawDoc coLawDoc = coLawDocMapper.selectOne(query);
            String docNameCn = coLawDoc.getDocNameCn();
            //拼接指向法律条文
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("《").append(docNameCn).append("》")
                    .append(StringUtils.isEmpty(lawPoint.get("chapterNumber")) ? "" : "第" + lawPoint.get("chapterNumber") + "章")
                    .append(StringUtils.isEmpty(lawPoint.get("passageNumber")) || "0".equals(lawPoint.get("passageNumber")) ? "" : "第" + lawPoint.get("passageNumber") + "节")
                    .append(StringUtils.isEmpty(lawPoint.get("itemNumber")) ? "" : "第" + lawPoint.get("itemNumber") + "条")
                    .append(StringUtils.isEmpty(lawPoint.get("xiangNumber")) ? "" : "第" + lawPoint.get("xiangNumber") + "项")
                    .append(StringUtils.isEmpty(lawPoint.get("kuanNumber")) ? "" : "第" + lawPoint.get("kuanNumber") + "款")
                    .append(StringUtils.isEmpty(lawPoint.get("muNumber")) ? "" : "第" + lawPoint.get("muNumber") + "目");

            String point = stringBuilder.toString();
            Article article = new Article();
            article.setPoint(point);
            articleList.add(article);
        }
        return articleList;
    }

}

package com.dcas.market.utils;

import cn.hutool.core.util.StrUtil;
import com.dcas.market.app.constant.AppConstant;

/**
 * <AUTHOR>
 * @className FirewallCmdUtil
 * @description 防火墙命令工具类
 * @date 2024/06/11 18:44
 */
public class FirewallCmdUtil {

    private final ShellUtil shellUtil;
    public FirewallCmdUtil(ShellUtil shellUtil){
        this.shellUtil = shellUtil;
    }

    public String openPort(String port) throws Exception {
        return shellUtil.execCmd(String.format("firewall-cmd --zone=public --add-port=%s/tcp --permanent", port));
    }

    public String closePort(String port) throws Exception {
        return shellUtil.execCmd(String.format("firewall-cmd --zone=public --remove-port=%s/tcp --permanent", port));
    }

    public String reload() throws Exception {
        return shellUtil.execCmd("firewall-cmd --reload");
    }

    public String status() throws Exception {
        return shellUtil.execCmd("firewall-cmd --state");
    }

    public String listPorts() throws Exception {
        return shellUtil.execCmd("firewall-cmd --list-ports");
    }
}

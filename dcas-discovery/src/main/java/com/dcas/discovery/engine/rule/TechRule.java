package com.dcas.discovery.engine.rule;

import com.dcas.discovery.engine.EngineContext;
import lombok.Getter;

public abstract class TechRule extends BusinessRule<TechRule.TechRuleResult> {
    public static final TechRule EMPTY = new DefaultTechRule();

    public TechRule(Long bizId, String bizName) {
        super(bizId, bizName, 0, 0, null, null);
    }

    public abstract TechRuleResult process(EngineContext context);

    protected abstract String getType();

    @Getter
    public class TechRuleResult extends BusinessRule<TechRuleResult>.BusinessRuleResult {
        private final String type;
        private final Double score;

        public TechRuleResult(boolean match, String type, Double score) {
            super(match);
            this.type = type;
            this.score = score;
        }

        @Override
        public RuleType getRuleType() {
            return RuleType.TECH;
        }
    }

    private static class DefaultTechRule extends TechRule {
        public DefaultTechRule() {
            super(null, null);
        }

        @Override
        public TechRuleResult process(EngineContext context) {
            return new TechRuleResult(false, null, null);
        }

        @Override
        protected String getType() {
            return null;
        }
    }

    @Override
    public String toString() {
        return "TechRule{" + "type='" + getType() + '\'' + '}';
    }
}
